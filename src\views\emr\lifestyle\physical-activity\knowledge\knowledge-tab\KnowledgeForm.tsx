import { FC, memo } from 'react';

import { MdOutlinePsychology } from 'react-icons/md';

import RenderFields from '@/views/emr/lifestyle/shared/render-fields';

import { FieldGroup } from '@/types/emr/lifestyle/questionnaire';

type Props = {
  formFields: FieldGroup[];
  readonly?: boolean;
  showHeading?: boolean;
};

const KnowledgeForm: FC<Props> = ({
  formFields,
  readonly,
  showHeading = true,
}) => {
  const renderIcon = (icon: string) => {
    if (icon === 'psychology') {
      return <MdOutlinePsychology className="text-2xl text-black" />;
    }
    return <span className="text-2xl">{icon}</span>;
  };

  if (!formFields || formFields.length === 0) {
    return (
      <div className="p-4 text-center text-gray-500">
        No knowledge data available
      </div>
    );
  }

  return (
    <div
      className={`space-y-6 p-4 ${readonly ? 'pointer-events-none readonly-form' : ''}`}
    >
      {formFields?.map((section, index) => (
        <div key={section.id || index} className="space-y-6">
          {showHeading && (
            <div className="flex items-center space-x-2 mb-4">
              {section.icon && renderIcon(section.icon)}
              <h3 className="text-lg font-medium">{section.title}</h3>
            </div>
          )}
          <div>
            <RenderFields
              fields={section?.fields}
              namePrefix={`questions.${index}.fields`}
              readonly={readonly}
              groupClassName="space-y-0"
            />
          </div>
        </div>
      ))}
    </div>
  );
};

export default memo(KnowledgeForm);
