'use client';

import React, { useMemo } from 'react';

import { Control, useWatch } from 'react-hook-form';

import { NICOTINE_DEPENDENCE_TEST_QUESTIONS } from '@/constants/emr/lifestyle/medical-history-addiction';

import ControlledRadio from '@/components/controlled-inputs/ControlledRadio';

import AppButton from '@/core/components/app-button';
import AppIcon from '@/core/components/app-icon';
import AppModal from '@/core/components/app-modal';
import { MedicalHistoryAddictionForm } from '@/types/emr/lifestyle/medical-history-addiction';

interface NicotineDependenceTestProps {
  isOpen: boolean;
  onClose: () => void;
  control: Control<MedicalHistoryAddictionForm>;
  onSave: (testResult: any) => void;
}

const NicotineDependenceTest: React.FC<NicotineDependenceTestProps> = ({
  isOpen,
  onClose,
  control,
  onSave,
}) => {
  const testValues = useWatch<MedicalHistoryAddictionForm>({
    control,
    name: 'nicotineDependenceTest.responses',
    defaultValue: {
      timeToFirstCigarette: '',
      findDifficult: '',
      whichCigarette: '',
      cigarettesPerDay: '',
      moreFrequentMorning: '',
      smokeWhenIll: '',
    },
  });

  const totalScore = useMemo(() => {
    let score = 0;
    NICOTINE_DEPENDENCE_TEST_QUESTIONS.forEach((question) => {
      const selectedValue =
        testValues?.[question.name as keyof typeof testValues];
      if (selectedValue) {
        const selectedOption = question.options.find(
          (option) => option.label === selectedValue
        );
        if (selectedOption) {
          score += selectedOption.score;
        }
      }
    });
    return score;
  }, [testValues]);

  const handleSave = () => {
    const testResult = {
      responses: testValues,
      testDate: new Date().toISOString(),
      totalScore,
    };

    onSave(testResult);
    onClose();
  };

  return (
    <AppModal
      open={isOpen}
      onClose={onClose}
      title="Nicotine Dependence Test"
      classes={{
        root: 'w-[65vw] h-[95vh] flex flex-col min-h-0',
        body: 'flex-1 h-full flex flex-col overflow-hidden min-h-0 p-0',
      }}
    >
      <div className="flex flex-col h-full">
        {/* Questions Section */}
        <div className="flex-1 overflow-y-auto border-2">
          <div className="w-full">
            {NICOTINE_DEPENDENCE_TEST_QUESTIONS.map((question, index) => (
              <div
                key={question.name}
                className={`grid grid-cols-2 min-h-[80px] ${
                  index % 2 === 0 ? 'bg-[#E6F6FF]' : 'bg-white'
                }`}
              >
                {/* Question Column */}
                <div className="p-base border-r border-gray-200 flex items-center">
                  <p className="text-[12px] text-gray-900 font-medium">
                    {question.questionText}
                  </p>
                </div>

                {/* Options Column */}
                <div className="p-base flex items-center">
                  <div className="min-w-90 text-[14px]">
                    {question.options?.map((option) => (
                      <div key={option?.label} className="py-1">
                        {option?.label}
                      </div>
                    ))}
                  </div>
                  <div className="w-full space-y-1">
                    <ControlledRadio
                      name={
                        `nicotineDependenceTest.responses.${question.name}` as any
                      }
                      control={control}
                      row={false}
                      sx={{
                        '& .MuiSvgIcon-root': {
                          fontSize: 16,
                        },
                        '& .MuiFormControlLabel-label': {
                          fontSize: '14px',
                        },
                      }}
                      options={question.options.map((option) => ({
                        value: option.label,
                        label: (
                          <div className="flex items-center w-full text-sm">
                            {option.score}
                          </div>
                        ),
                      }))}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Total Score Section */}
        <div className="bg-white border-x-2">
          <div className="flex w-full justify-end">
            <div className="px-4 flex items-center justify-between border-l py-base w-[calc(50%+5px)]">
              <span className="text-sm font-semibold text-gray-900">
                Total Score
              </span>
              <span className="text-sm font-bold text-gray-900 pr-25">
                {totalScore}
              </span>
            </div>
          </div>
        </div>

        {/* Score Interpretation Section */}
        <div className="bg-[#B4E5FE] p-base border-2">
          <div className="flex items-center gap-6">
            <p className="text-sm font-semibold text-black">Score</p>
            <div className="text-sm text-black">
              <p>1-2 = Low Dependence</p>
              <p>3-4 = low- mod dependence</p>
            </div>
            <div className="text-sm text-black">
              <p>5-7 = moderate dependence</p>
              <p>&gt;8 = high dependence</p>
            </div>
          </div>
        </div>

        {/* Save Button */}
        <div className="p-base pb-0 bg-white border-t border-gray-300 flex justify-start">
          <AppButton
            type="button"
            onClick={handleSave}
            className="bg-slate-800 text-white hover:bg-slate-700"
            endIcon={<AppIcon icon="material-symbols:save-outline" />}
            sx={{
              borderRadius: 2,
              backgroundColor: '#1e293b',
              '&:hover': {
                backgroundColor: '#334155',
              },
            }}
          >
            Save Record
          </AppButton>
        </div>
      </div>
    </AppModal>
  );
};

export default NicotineDependenceTest;
