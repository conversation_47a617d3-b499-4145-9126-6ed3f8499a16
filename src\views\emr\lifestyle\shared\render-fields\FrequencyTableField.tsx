import { FC, memo, useState } from 'react';

import { Controller } from 'react-hook-form';

import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Radio,
  Table,
  TableBody,
  TableCell,
  TableRow,
  Typography,
  TableCellProps,
} from '@mui/material';

import AppIcon from '@/core/components/app-icon';
import {
  StyledTableContainerV2,
  StyledTableHeadV2,
} from '@/core/components/table-v2/styled-components';
import { FieldGroup } from '@/types/emr/lifestyle/questionnaire';

import { FieldComponentProps } from './types';

interface FrequencyTableFieldProps extends Partial<FieldComponentProps> {
  sections: FieldGroup[];
  control: any;
  readonly?: boolean;
  expandedSections?: string[];
  onExpandAll?: () => void;
  onCollapseAll?: () => void;
  onSectionToggle?: (sectionId: string) => void;
}

const Headers = {
  FOOD_ITEM: 'Food Item',
  DAILY: 'Daily',
  WEEKLY: 'Weekly (1,2,3,4,5)',
  MONTHLY: 'Monthly (1,2)',
  RARELY: 'Rarely',
  NEVER: 'Never',
} as const;

type Headers = (typeof Headers)[keyof typeof Headers];

const { DAILY, FOOD_ITEM, MONTHLY, NEVER, RARELY, WEEKLY } = Headers;

const commonStyling = { padding: '6px 12px', fontSize: '12px' };

const cellStyling: Record<string, TableCellProps> = {
  [FOOD_ITEM]: { sx: { ...commonStyling, minWidth: 120, maxWidth: 120 } },
  [DAILY]: { sx: { ...commonStyling, minWidth: 50, maxWidth: 50 } },
  [WEEKLY]: { sx: { ...commonStyling, minWidth: 160, maxWidth: 160 } },
  [MONTHLY]: { sx: { ...commonStyling, minWidth: 120, maxWidth: 120 } },
  [RARELY]: { sx: { ...commonStyling, minWidth: 50, maxWidth: 50 } },
  [NEVER]: { sx: { ...commonStyling, minWidth: 50, maxWidth: 50 } },
};

const FrequencyTableField: FC<FrequencyTableFieldProps> = ({
  sections,
  control,
  readonly,
  expandedSections: propExpandedSections,
  onSectionToggle,
}) => {
  const [internalExpandedSections, setInternalExpandedSections] = useState<
    string[]
  >(['cereals_grains']);

  const expandedSections = propExpandedSections ?? internalExpandedSections;

  const handleSectionToggle = (sectionId: string) => {
    if (onSectionToggle) {
      onSectionToggle(sectionId);
    } else {
      setInternalExpandedSections((prev) =>
        prev.includes(sectionId)
          ? prev.filter((id) => id !== sectionId)
          : [...prev, sectionId]
      );
    }
  };

  const firstField = sections[0]?.fields?.[0] as any;
  const frequencyColumns = firstField?.columns || [];

  const columnHeaders = [
    { key: 'food_item', label: 'Food Item' },
    ...frequencyColumns.map((col: any) => ({
      key: col.header,
      label: col.header,
      options: col.option || [],
    })),
  ];

  return (
    <div className="w-full">
      <StyledTableContainerV2
        sx={{
          ...(readonly && {
            '& .MuiTableHead-root .MuiTableCell-root': {
              backgroundColor: '#64707D',
            },
          }),
        }}
      >
        <Table size="small" stickyHeader>
          <StyledTableHeadV2>
            <TableRow>
              {columnHeaders.map((header) => (
                <TableCell
                  key={header.key}
                  align="center"
                  {...(cellStyling?.[header?.label] || {})}
                >
                  {header.label}
                </TableCell>
              ))}
            </TableRow>
          </StyledTableHeadV2>
        </Table>

        {sections.map((section, sectionIndex) => (
          <Accordion
            key={section.id}
            expanded={expandedSections.includes(section.id)}
            onChange={() => handleSectionToggle(section.id)}
            sx={{
              boxShadow: 'none',
              borderLeft: '1.5px solid #003366',
              borderRight: '1.5px solid #003366',
              borderBottom: '1px solid #003366',
              borderTop: 'none',
              '&:before': { display: 'none' },
              '&.Mui-expanded': { margin: 0 },
            }}
            classes={{ heading: 'bg-transparent !h-12 !flex items-center' }}
          >
            <AccordionSummary
              expandIcon={
                <AppIcon icon="icon-park-outline:down" className="text-black" />
              }
              classes={{
                content: '',
                expanded:
                  '!bg-transparent !min-h-10 !h-10 !m-0 flex items-center',
                root: '!bg-transparent min-h-10 border-b border-black',
                expandIconWrapper:
                  '!bg-transparent min-h-10 flex items-center ',
              }}
            >
              <Typography variant="subtitle1" fontWeight="medium">
                {section.title}
              </Typography>
            </AccordionSummary>

            <AccordionDetails classes={{ root: '!p-0' }}>
              <Table size="small">
                <TableBody>
                  {section.fields?.map((field, fieldIndex) => (
                    <TableRow
                      key={field.id}
                      sx={{
                        backgroundColor:
                          fieldIndex % 2 === 0 ? '#e3f2fd' : 'white',
                        '&:hover': { backgroundColor: '#f8f9fa' },
                      }}
                    >
                      {/* Food Item Column */}
                      <TableCell {...cellStyling?.[FOOD_ITEM]} align="left">
                        <div className="flex justify-start text-left">
                          {field.label}
                        </div>
                      </TableCell>

                      {frequencyColumns.map((column: any, colIndex: number) => (
                        <TableCell
                          key={`${field.id}_${column.header}_${colIndex}`}
                          id={`${field.id}_${column.header}_${colIndex}`}
                          align="center"
                          {...cellStyling?.[column?.header]}
                          sx={{
                            ...(cellStyling?.[column?.header]?.sx ?? {}),
                            p: '0px',
                          }}
                        >
                          <div className="flex gap-1 justify-center">
                            {column.option?.map((option: string) => (
                              <Controller
                                key={`${field.id}_${option}`}
                                name={`questions.0.sections.${sectionIndex}.fields.${fieldIndex}.value`}
                                control={control}
                                render={({ field: controllerField }) => {
                                  const isChecked =
                                    controllerField.value === option;

                                  return (
                                    <Radio
                                      value={option}
                                      checked={isChecked}
                                      size="small"
                                      onChange={(e) =>
                                        !readonly &&
                                        controllerField.onChange(e.target.value)
                                      }
                                      disableRipple={readonly}
                                      disableTouchRipple={readonly}
                                      disabled={readonly}
                                      sx={{
                                        color: '#000000 !important',
                                        '&.Mui-checked': {
                                          color: '#000000 !important',
                                        },
                                        '&.Mui-disabled': {
                                          color: '#000000 !important',
                                          opacity: 1,
                                        },
                                        '&.Mui-disabled.Mui-checked': {
                                          color: '#000000 !important',
                                          opacity: 1,
                                        },
                                        '& .MuiSvgIcon-root': {
                                          color: '#000000 !important',
                                        },
                                        '&.Mui-disabled .MuiSvgIcon-root': {
                                          color: '#000000 !important',
                                          opacity: 1,
                                        },
                                      }}
                                    />
                                  );
                                }}
                              />
                            ))}
                          </div>
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </AccordionDetails>
          </Accordion>
        ))}
      </StyledTableContainerV2>
    </div>
  );
};

export default memo(FrequencyTableField);
