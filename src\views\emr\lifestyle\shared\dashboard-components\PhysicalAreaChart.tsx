import React from 'react';

import {
  ResponsiveContainer,
  AreaChart,
  Area,
  Line,
  XAxis,
  <PERSON>Axis,
  CartesianGrid,
  Tooltip,
} from 'recharts';

interface LineChartProps {
  data: { date: string; value: number }[];
  dataKey: string;
  color: string;
  title?: string;
  unit?: string;
  height?: number;
  showLegend?: boolean;
  yAxisDomain?: [number, number];
}

const PhysicalAreaChart: React.FC<LineChartProps> = ({
  data,
  dataKey,
  color,
  title,
  unit,
  height = 300,
  showLegend = true,
  yAxisDomain,
}) => {
  return (
    <div className="w-full">
      {title && <h3 className="text-sm font-semibold mb-2">{title}</h3>}
      <ResponsiveContainer width="100%" height={height}>
        <AreaChart
          data={data}
          margin={{ top: 20, right: 20, left: 0, bottom: 20 }}
        >
          <defs>
            <linearGradient id="fadingArea" x1="0" y1="0" x2="0" y2="1">
              <stop offset="0%" stopColor={color} stopOpacity={0.6} />
              <stop offset="100%" stopColor={color} stopOpacity={0} />
            </linearGradient>
          </defs>

          <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
          <XAxis
            dataKey="date"
            axisLine={false}
            tickLine={false}
            tick={{ fill: '#6b7280', fontSize: 12 }}
          />
          <YAxis
            axisLine={false}
            tickLine={false}
            domain={yAxisDomain}
            tick={{ fill: '#6b7280', fontSize: 12 }}
          />
          <Tooltip formatter={(value: any) => `${value}${unit || ''}`} />
          <Area
            type="linear"
            dataKey={dataKey}
            stroke="none"
            fill="url(#fadingArea)"
            isAnimationActive={false}
            connectNulls
          />
          <Line
            type="linear"
            dataKey={dataKey}
            stroke={color}
            strokeWidth={2}
            dot={{ r: 4, fill: color, strokeWidth: 1, stroke: '#fff' }}
            activeDot={{ r: 5, fill: color, stroke: '#fff', strokeWidth: 1 }}
            connectNulls
            isAnimationActive={false}
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
};

export default PhysicalAreaChart;
