import { useState, useCallback, useRef, useMemo, useEffect } from 'react';

import { components } from 'react-select';
import type { SingleValue } from 'react-select';

import { Box, Divider, Typography } from '@mui/material';

import { usePrescriptionStore } from '@/store/emr/prescription';

import { createDebouncedSearch } from '@/utils/search';

import AsyncSearch from '@/core/components/search';
import type { PrescriptionItem } from '@/types/emr/prescription';

interface MedicineOption {
  value: string;
  label: string;
  medicine: PrescriptionItem;
}

interface MedicineSearchProps {
  placeholder?: string;
  onChange?: (medicine: PrescriptionItem | null) => void;
}

const MedicineSearch: React.FC<MedicineSearchProps> = ({
  placeholder = 'Search by Medicine',
  onChange,
}) => {
  const { searchMedicines, selectMedicine } = usePrescriptionStore();

  const [inputValue, setInputValue] = useState('');
  const [selectedOption, setSelectedOption] = useState<MedicineOption | null>(
    null
  );
  const [isSearching, setIsSearching] = useState(false);

  // Enhanced refs for better race condition handling
  const abortControllerRef = useRef<AbortController | null>(null);
  const currentSearchRef = useRef<string>('');
  const searchRequestIdRef = useRef<number>(0);
  const pendingCallbacksRef = useRef<Set<number>>(new Set());

  // Create debounced search with enhanced error handling
  const { search, cancel } = useMemo(
    () =>
      createDebouncedSearch<PrescriptionItem>(
        async (searchTerm: string, signal?: AbortSignal) => {
          setIsSearching(true);
          try {
            // Check if request was aborted before making API call
            if (signal?.aborted) {
              throw new Error('Request aborted');
            }

            const results = await searchMedicines(searchTerm);

            // Check again after API call
            if (signal?.aborted) {
              throw new Error('Request aborted');
            }

            return results;
          } catch (error) {
            if (
              error instanceof Error &&
              error.name !== 'AbortError' &&
              error.message !== 'Request aborted'
            ) {
              console.error('Error searching medicines:', error);
            }
            throw error; // Re-throw to be handled by debounced function
          } finally {
            // Only update loading state if this is still the current search
            if (currentSearchRef.current === searchTerm) {
              setIsSearching(false);
            }
          }
        },
        300,
        {
          minLength: 1,
          maxWait: 800,
          onStart: () => {
            // Cancel any pending requests
            if (abortControllerRef.current) {
              abortControllerRef.current.abort();
            }
            abortControllerRef.current = new AbortController();
            setIsSearching(true);
          },
          onComplete: () => {
            abortControllerRef.current = null;
            setIsSearching(false);
          },
          onError: (error) => {
            // Only log non-abort errors
            if (
              error instanceof Error &&
              error.name !== 'AbortError' &&
              error.message !== 'Request aborted'
            ) {
              console.error('Search error:', error);
            }
            setIsSearching(false);
          },
        }
      ),
    [searchMedicines]
  );

  // Transform results to options
  const transformResults = useCallback(
    (medicines: PrescriptionItem[]): MedicineOption[] => {
      return medicines
        .filter((medicine) => !!medicine.id)
        .map((medicine) => ({
          value: medicine.id as string,
          label: medicine.BrandName || medicine.GenericName || '',
          medicine: { ...medicine, quantity: '1' },
        }));
    },
    []
  );

  // Enhanced load options function with better race condition handling
  const loadOptions = useCallback(
    (
      inputVal: string,
      callback: (options: readonly MedicineOption[]) => void
    ) => {
      const trimmedInput = inputVal.trim();

      // If input is empty, return empty options immediately
      if (trimmedInput.length === 0) {
        // Clear any pending requests
        cancel();
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
        }
        // Clear pending callbacks
        pendingCallbacksRef.current.clear();
        callback([]);
        return;
      }

      // Generate unique request ID
      const requestId = ++searchRequestIdRef.current;
      pendingCallbacksRef.current.add(requestId);

      // Update the current search ref
      currentSearchRef.current = trimmedInput;

      // Enhanced callback wrapper with request ID validation
      const wrappedCallback = (medicines: PrescriptionItem[]) => {
        // Check if this request is still valid
        if (!pendingCallbacksRef.current.has(requestId)) {
          return; // This request has been superseded
        }

        // Check if the search term is still current
        if (currentSearchRef.current !== trimmedInput) {
          pendingCallbacksRef.current.delete(requestId);
          return; // Search term has changed
        }

        try {
          const options = transformResults(medicines);

          // Final validation before calling callback
          if (
            currentSearchRef.current === trimmedInput &&
            pendingCallbacksRef.current.has(requestId)
          ) {
            callback(options);
          }
        } catch (error) {
          console.error('Error transforming results:', error);
          callback([]);
        } finally {
          pendingCallbacksRef.current.delete(requestId);
        }
      };

      // Error callback wrapper
      const errorCallback = () => {
        if (pendingCallbacksRef.current.has(requestId)) {
          pendingCallbacksRef.current.delete(requestId);
          // Only show empty results if this is still the current search
          if (currentSearchRef.current === trimmedInput) {
            callback([]);
          }
        }
      };

      // Use debounced search with enhanced error handling
      try {
        search(
          trimmedInput,
          wrappedCallback,
          abortControllerRef.current?.signal
        ).catch(errorCallback);
      } catch (error) {
        errorCallback();
      }
    },
    [search, cancel, transformResults]
  );

  // Handle input change with cleanup
  const handleInputChange = useCallback(
    (newValue: string, { action }: { action: string }) => {
      if (action === 'input-change') {
        setInputValue(newValue);
        const trimmedValue = newValue.trim();

        // If input is cleared, immediately clear everything
        if (!trimmedValue) {
          // Cancel all pending operations
          cancel();
          if (abortControllerRef.current) {
            abortControllerRef.current.abort();
          }
          pendingCallbacksRef.current.clear();
          currentSearchRef.current = '';
          setIsSearching(false);
        } else if (trimmedValue !== currentSearchRef.current) {
          // Only update search if the trimmed value has actually changed
          currentSearchRef.current = trimmedValue;
        }
      }
    },
    [cancel]
  );

  // Enhanced cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      cancel();
      pendingCallbacksRef.current.clear();
    };
  }, [cancel]);

  const handleChange = (newValue: SingleValue<MedicineOption>) => {
    const medicine = newValue ? newValue.medicine : null;
    if (medicine) {
      const medicineWithDefaults = {
        ...medicine,
        quantity: '1',
        cost: String(medicine.Cost || '0'),
      };
      selectMedicine(medicineWithDefaults);
      if (onChange) onChange(medicineWithDefaults);
    } else {
      selectMedicine(null);
      if (onChange) onChange(null);
    }

    setSelectedOption(newValue);
    setInputValue('');

    // Clear selection after a short delay
    setTimeout(() => {
      setSelectedOption(null);
    }, 800);
  };

  const CustomOption = (props: any) => {
    const { medicine } = props.data;

    const medicineDetails = [
      { label: medicine.DrugFormulation, flex: 2 },
      { label: medicine.BrandName, flex: 4 },
      { label: medicine.Strength, flex: 4 },
      { label: medicine.Measure, flex: 1 },
      { label: medicine.UnitOfMeasure, flex: 1 },
    ];

    return (
      <>
        <components.Option {...props}>
          <Box display="flex" alignItems="center" px={1} py={0}>
            {medicineDetails.map((detail, index) => (
              <Box
                key={index}
                flex={detail.flex}
                textAlign="left"
                ml={index > 0 ? 1 : 0}
              >
                <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
                  {detail.label}
                </Typography>
              </Box>
            ))}
          </Box>
        </components.Option>
        <Divider sx={{ my: 0 }} />
      </>
    );
  };

  return (
    <AsyncSearch<MedicineOption>
      loadOptions={loadOptions}
      onChange={handleChange}
      placeholder={placeholder}
      defaultOptions={[]}
      isLoading={isSearching}
      cacheOptions={false} // Disable caching to avoid stale results
      components={{
        Option: CustomOption,
        LoadingIndicator: () => null, // Hide default loading indicator with empty component
      }}
      value={selectedOption}
      inputValue={inputValue}
      onInputChange={handleInputChange}
      noOptionsMessage={({ inputValue }) => {
        if (isSearching) return 'Searching...';
        return inputValue.trim().length === 0
          ? 'Start typing to search medicines'
          : 'No medicines found';
      }}
    />
  );
};

export default MedicineSearch;
