import axios from 'axios';

import API_CONFIG from '@/core/configs/api';
import { api, arcaAxios } from '@/core/lib/interceptor';

const { SPEECH_ENGINE_SUBSCRIPTION_KEY } = API_CONFIG;

export async function getSpeechToken() {
  const headers = {
    headers: {
      'Ocp-Apim-Subscription-Key': SPEECH_ENGINE_SUBSCRIPTION_KEY,
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  };

  try {
    const tokenResponse = await axios.post(
      `https://eastus.api.cognitive.microsoft.com/sts/v1.0/issueToken`,
      null,
      headers
    );
    return tokenResponse.data;
  } catch (err) {
    console.error(err);
  }
}

type SummarizeConversationApiRes = {
  conversation: [
    {
      speaker: 'doctor' | 'patient';
      message: string;
    },
  ];
  summary: {
    presentingcomplaint: string;
    historyofpresenting: string;
    pastmedicalhistory: string;
    pastsurgicalhistory: string;
    familyhistory: string;
    addictionhistory: string;
    diethistory: string;
    physicalactivityhistory: string;
    stresshistory: string;
    sleephistory: string;
    currentmedicationhistory: string;
  };
};

export type Conversation = {
  speaker: 'doctor' | 'patient';
  message: string;
};

export type SummarizeConversationRes = {
  conversation: Conversation[];
  summary: {
    owner?: {
      id: string;
      role: string;
      name: string;
      email: string;
    };
    conversation?: Conversation[];
    // CamelCase properties
    presentingComplaints?: string;
    historyOfPresenting?: string;
    pastMedicalHistory?: string;
    pastSurgicalHistory?: string;
    familyHistory?: string;
    addictionHistory?: string;
    dietHistory?: string;
    physicalActivityHistory?: string;
    stressHistory?: string;
    sleepHistory?: string;
    currentMedicationHistory?: string;

    // Snake_case properties from API
    presentingcomplaint?: string;
    historyofpresenting?: string;
    pastmedicalhistory?: string;
    pastsurgicalhistory?: string;
    familyhistory?: string;
    addictionhistory?: string;
    diethistory?: string;
    physicalactivityhistory?: string;
    stresshistory?: string;
    sleephistory?: string;
    currentmedicationhistory?: string;
    vitals?: {
      heartRate?: number;
      systolicPressure?: number;
      diastolicPressure?: number;
      respiratoryRate?: number;
      spO2?: number;
      temperature?: number;
    };
    anthropometry?: {
      height?: number | string;
      weight?: number | string;
      bmi?: number | string;
      waistCircumference?: number | string;
    };
    generalPhysicalExamination?: {
      pallor?: boolean;
      icterus?: boolean;
      cyanosis?: boolean;
      clubbing?: boolean;
      pedalEnema?: boolean;
      lymphadenopathy?: boolean;
      notes?: string;
      pedalEnemaNotes?: string;
      lymphadenopathyNotes?: string;
    };
    heent?: string;
    systemicExamination?: {
      neurologicalExamination?: string;
      cardiovascularExamination?: string;
      respiratoryExamination?: string;
      abdomenExamination?: string;
      rheumatologicalExamination?: string;
    };
  };
};

export async function summarizeAmbientListening(
  source: string,
  transcript: string
) {
  // Format the transcript with proper line breaks

  const { data } = await api.post<SummarizeConversationApiRes>(
    '/lifestyle/v0.1/lifestyle/ambient-listening',
    {
      source,
      transcript,
    }
  );

  return data;
}

export async function summarizeConversation(
  transcript: string
): Promise<SummarizeConversationRes> {
  const { data } = await arcaAxios.post<SummarizeConversationApiRes>(
    '/summary',
    transcript,
    {
      headers: {
        'Content-Type': 'text/plain',
      },
    }
  );

  const res = {
    conversation: data.conversation,
    summary: {
      presentingComplaints: data.summary.presentingcomplaint,
      historyOfPresenting: data.summary.historyofpresenting,
      pastMedicalHistory: data.summary.pastmedicalhistory,
      pastSurgicalHistory: data.summary.pastmedicalhistory,
      familyHistory: data.summary.familyhistory,
      addictionHistory: data.summary.addictionhistory,
      dietHistory: data.summary.diethistory,
      physicalActivityHistory: data.summary.physicalactivityhistory,
      stressHistory: data.summary.stresshistory,
      sleepHistory: data.summary.sleephistory,
      currentMedicationHistory: data.summary.currentmedicationhistory,
    },
  };

  return res;
}
