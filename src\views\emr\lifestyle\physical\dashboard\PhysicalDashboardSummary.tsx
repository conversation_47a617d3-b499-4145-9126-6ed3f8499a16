import React from 'react';

import avgCalorie from '@/assets/svg/physical-activity/avgCalorie.png';
import avgDuration from '@/assets/svg/physical-activity/avgDuration.png';
import avgIntensity from '@/assets/svg/physical-activity/avgIntensity.png';
import totalCalorie from '@/assets/svg/physical-activity/totalCalorie.png';
import totalDuration from '@/assets/svg/physical-activity/totalDuration.png';
import totalSession from '@/assets/svg/physical-activity/totalSession.png';

import AverageCard from '../../shared/dashboard-components/AverageCard';

export interface PhysicalSummary {
  totalCaloriesSpent: number;
  totalSessions: number;
  totalDuration: number;
  avgCaloriesSpent: number;
  avgIntensity: string;
  avgDuration: number;
}

const PhysicalDashboardSummary: React.FC<{ summary: PhysicalSummary }> = ({
  summary,
}) => (
  <div>
    <h2 className="text-lg font-semibold mb-4">Summary</h2>
    <div className="grid grid-cols-3 gap-4 mb-6">
      <AverageCard
        title="Total Calorie Spent"
        value={summary.totalCaloriesSpent}
        unit="kcal"
        icon={<img src={totalCalorie.src} alt="Oil" width={24} height={24} />}
        bgColor="#FDEDE7"
        borderColor="#F26430"
      />
      <AverageCard
        title="Total Sessions"
        value={summary.totalSessions}
        unit=""
        icon={<img src={totalSession.src} alt="Oil" width={24} height={24} />}
        bgColor="#E9FBF9"
        borderColor="#1B998B"
      />
      <AverageCard
        title="Total Duration"
        value={summary.totalDuration}
        unit="mins"
        icon={<img src={totalDuration.src} alt="Oil" width={24} height={24} />}
        bgColor="#F5F8EC"
        borderColor="#A8C256"
      />

      <AverageCard
        title="Avg. Calorie Spent"
        value={summary.avgCaloriesSpent}
        unit="kcal"
        icon={<img src={avgCalorie.src} alt="Oil" width={24} height={24} />}
        bgColor="#E9F7FC"
        borderColor="#0B3948"
      />
      <AverageCard
        title="Avg. Intensity"
        value={summary.avgIntensity}
        unit=""
        icon={<img src={avgIntensity.src} alt="Oil" width={24} height={24} />}
        bgColor="#FAEBEE"
        borderColor="#BA324F"
      />
      <AverageCard
        title="Avg. Duration"
        value={summary.avgDuration}
        unit="mins"
        icon={<img src={avgDuration.src} alt="Oil" width={24} height={24} />}
        bgColor="#E5FCFF"
        borderColor="#00ACC1"
      />
    </div>
  </div>
);

export default PhysicalDashboardSummary;
