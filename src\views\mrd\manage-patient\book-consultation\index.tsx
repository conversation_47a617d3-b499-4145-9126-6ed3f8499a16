'use client';

import React, { FC, memo, useEffect, useCallback, useMemo } from 'react';

import { FormProvider, useForm } from 'react-hook-form';

import { yupResolver } from '@hookform/resolvers/yup';
import { BiCheck } from 'react-icons/bi';
import { MdOutlineKeyboardArrowRight } from 'react-icons/md';

import { useRouter } from 'next/navigation';

import usePaymentPermissions from '@/hooks/usePaymentPermissions';

import { useBookConsultationStore } from '@/store/mrd/manage-patient/book-consultation';
import { useManagePatientStore } from '@/store/mrd/manage-patient/manage';
import { usePaymentStore } from '@/store/payments';
import { useUserStore } from '@/store/userStore';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { addDateAndTime, formatDate } from '@/utils/dateUtils/dayUtils';
import { getAppointmentId } from '@/utils/mrd/manage-patient/get-appointment-id';

import {
  AppointmentStatus,
  ConsultationView,
  PatientStatus,
} from '@/constants/mrd/manage-patient/consultation';
import { routes } from '@/constants/routes';

import PaymentConfirmationModal from '@/views/mrd/payment/PaymentConfirmationModal';
import PaymentFailureModal from '@/views/mrd/payment/PaymentFailureModal';

import AppButton from '@/core/components/app-button';
import Loader from '@/core/components/app-loaders/Loader';
import AppTitle from '@/core/components/app-title';
import {
  ConsultationForm,
  defaultConsultation,
} from '@/types/mrd/manage-patient/consultation';

import KeyValuePair from '../shared/KeyValuePair';

import BookConsultationForm from './BookConsultationForm';
import BookConsultationPreview from './BookConsultationPreview';
import { consultationSchema } from './validation';

const PAYMENT_STATUS = {
  PAID: 'PAID',
  PENDING: 'PENDING',
} as const;

type Props = {
  id: string;
};

const BookConsultation: FC<Props> = ({ id }) => {
  const router = useRouter();
  const {
    patient,
    getPatientById,
    loading: patientLoading,
  } = useManagePatientStore();

  const {
    fetchAllDoctors,
    setView,
    view,
    createAppointments,
    updatingAppointment,
    getFutureAppointments,
    appointments,
    doctors,
    reset: resetStore,
    futureAppointmentsLoading,
  } = useBookConsultationStore();

  const methods = useForm<{ consultation: ConsultationForm[] }>({
    defaultValues: { consultation: [defaultConsultation] },
    resolver: yupResolver(consultationSchema) as any,
  });

  const { handleSubmit, trigger, reset } = methods;

  const consultationComponent = useMemo(() => {
    switch (view) {
      case ConsultationView.form:
        return <BookConsultationForm />;
      case ConsultationView.preview:
        return <BookConsultationPreview />;
      default:
        return <BookConsultationForm />;
    }
  }, [view]);

  const handleClickCancel = useCallback(() => {
    router.push(routes.MRD_MANAGE_PATIENTS);
  }, [router]);

  const onSubmit = useCallback(
    async (data: { consultation: ConsultationForm[] }) => {
      try {
        const consultation = data.consultation.map((item) => {
          const baseItem = {
            ...item,
            patientId: patient?.id ?? '',
            doctorId: item.doctorId?.id ?? '',
            status:
              `${AppointmentStatus.Booked}-${PatientStatus.Booked}` as const,
            date: addDateAndTime(item.date, item.time),
            id: item?.id ?? getAppointmentId(),
          };

          // Only include payment status if it's PAID
          if (item.paymentStatus === PAYMENT_STATUS.PAID) {
            return {
              ...baseItem,
              paymentStatus: PAYMENT_STATUS.PAID,
            };
          }
          return baseItem;
        });
        await createAppointments(consultation);
        handleClickCancel();
      } catch (error) {
        console.error(error);
      }
    },
    [createAppointments, patient?.id, handleClickCancel]
  );

  const handleClickNext = useCallback(async () => {
    const isValid = await trigger();
    handleSubmit((_data) => {})();
    if (!isValid) return;
    setView(ConsultationView.preview);
  }, [setView, trigger, handleSubmit]);

  const handleClickPrevious = useCallback(() => {
    setView(ConsultationView.form);
  }, [setView]);

  // Watch the entire form values to track nested changes
  const formValues = methods.watch();

  // Compute total consultation amount from selected consultations (sum of doctors' consultationFee)
  // Exclude consultations that are already paid (paymentStatus === 'PAID')
  const totalAmount = useMemo(() => {
    const consultations = formValues.consultation || [];
    return consultations.reduce((acc, item) => {
      // Skip if already paid
      if (item.paymentStatus === PAYMENT_STATUS.PAID) {
        return acc;
      }
      const fee = Number(item?.doctorId?.consultationFee ?? 0);
      return acc + (isNaN(fee) ? 0 : fee);
    }, 0);
  }, [formValues]);

  const { data: userData } = useUserStore();
  const { isPaymentEnabled } = usePaymentPermissions();
  const {
    createOrder,
    openRazorpayPayment,
    showPaymentModal,
    setShowPaymentModal,
    showFailureModal,
    resetPaymentState,
    handlePaymentSuccess,
    handlePaymentFailure,
    isCreatingOrder,
  } = usePaymentStore();

  const handleClickConfirmBooking = useCallback(() => {
    handleSubmit(onSubmit)();
  }, [handleSubmit, onSubmit]);

  const isAppointmentBookingPaymentEnabled = isPaymentEnabled(
    'appointmentBookingEnabled'
  );

  const handleOpenPaymentModal = useCallback(() => {
    setShowPaymentModal(true);
  }, [setShowPaymentModal]);

  const handleProceedToPaymentFromModal = useCallback(async () => {
    const consultationData = methods.getValues().consultation[0];
    const doctor = consultationData.doctorId;

    if (!doctor || !userData?.organizationId || !patient?.id) {
      console.error('Missing required data for payment');
      return;
    }

    const computedTotal = totalAmount || 0;

    if (!computedTotal) {
      handleClickConfirmBooking();
      setShowPaymentModal(false);
      return;
    }

    try {
      const paymentData = {
        amount: computedTotal,
        currency: 'INR',
        paymentType: 'consultation',
        organizationId: userData.organizationId,
        patientId: patient.id,
        description: `Consultation with Dr. ${doctor.name}`,
        metadata: {
          doctorId: doctor.id,
        },
      };

      const order = await createOrder(paymentData);
      if (!order?.success) {
        setShowPaymentModal(false);
        return;
      }

      setShowPaymentModal(false);
      await openRazorpayPayment(
        order,
        patient.name || 'Patient',
        async (response) => {
          try {
            await handleSubmit(async (data) => {
              const updatedData = {
                ...data,
                consultation: data.consultation.map((consult) => ({
                  ...consult,
                  paymentStatus: PAYMENT_STATUS.PAID,
                  consultationFee: consult.consultationFee
                    ? consult.consultationFee
                    : consult.doctorId?.consultationFee || null,
                })),
              };
              await onSubmit(updatedData);
            })();
            handlePaymentSuccess();
          } catch (err) {
            console.error(
              'Payment succeeded but appointment creation failed:',
              err
            );
            handlePaymentFailure(
              'Payment successful but failed to create appointment'
            );
          }
        },
        (error) => {
          console.error(`Payment failed for UID: ${id}:`, error);
          handlePaymentFailure(error?.message || 'Payment failed');
        }
      );
    } catch (error) {
      console.error('Error in payment process:', error);
      setShowPaymentModal(false);
    }
  }, [
    createOrder,
    handleClickConfirmBooking,
    handlePaymentSuccess,
    handlePaymentFailure,
    methods,
    patient,
    openRazorpayPayment,
    setShowPaymentModal,
    userData,
    totalAmount,
  ]);

  const handleClosePaymentModal = useCallback(() => {
    setShowPaymentModal(false);
  }, [setShowPaymentModal]);

  const handleCloseFailureModal = useCallback(() => {
    resetPaymentState();
  }, [resetPaymentState]);

  const handleReset = useCallback(async () => {
    if (appointments.length === 0) {
      reset({ consultation: [defaultConsultation] });
    } else {
      const formattedConsultation = appointments.map((item) => {
        return {
          ...item,
          id: item?.appointmentId,
          doctorId: doctors.find((doc) => doc.id === item.doctorId) ?? null,
          date: formatDate(item.date, DateFormats.DATE_YYYY_MM_DD),
          type: item?.type ?? 'new',
        };
      });

      reset({ consultation: formattedConsultation });
    }
  }, [appointments, reset, doctors]);

  useEffect(() => {
    if (id) {
      getPatientById(id);
      getFutureAppointments(id);
    }
  }, [id, getPatientById, getFutureAppointments]);

  useEffect(() => {
    fetchAllDoctors();
  }, [fetchAllDoctors]);

  useEffect(() => {
    handleReset();
  }, [handleReset]);

  useEffect(() => resetStore(), [resetStore]);

  return (
    <FormProvider {...methods}>
      <form
        className="w-full h-full flex flex-col"
        onSubmit={handleSubmit(onSubmit)}
      >
        <AppTitle className="pb-base">Book Consultation</AppTitle>
        <div className="flex gap-base py-base border-b w-full">
          <div className="flex gap-base w-1/2">
            <KeyValuePair
              label="Name"
              value={patient?.name}
              loading={patientLoading}
            />
            <KeyValuePair
              label="Patient ID"
              value={patient?.id}
              loading={patientLoading}
            />
            <KeyValuePair
              label="Last Visited"
              loading={patientLoading}
              value={formatDate(
                patient?.last_consultation_date,
                DateFormats.DATE_DD_MM_YYYY_SLASH
              )}
            />
          </div>
        </div>
        <div className="flex-1 w-full h-full overflow-y-auto">
          {futureAppointmentsLoading ? (
            <div className="flex items-center justify-center h-full w-full">
              <Loader />
            </div>
          ) : (
            consultationComponent
          )}
          {view === ConsultationView.preview && (
            <div className="w-full flex justify-end pr-base py-2 border-t">
              <div className="text-right">
                <div className="text-sm text-text/70">Total Unpaid Amount</div>
                <div className="text-xl font-semibold">
                  {totalAmount.toFixed(2)}
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="flex gap-base w-full justify-end">
          {view === ConsultationView.form ? (
            <AppButton
              sx={{ minWidth: 180 }}
              size="small"
              variant="outlined"
              onClick={handleClickCancel}
            >
              Cancel
            </AppButton>
          ) : (
            <AppButton
              sx={{ minWidth: 180 }}
              size="small"
              variant="outlined"
              onClick={handleClickPrevious}
            >
              Reschedule
            </AppButton>
          )}
          {view === ConsultationView.form ? (
            <AppButton
              sx={{ minWidth: 180 }}
              size="small"
              onClick={handleClickNext}
              endIcon={<MdOutlineKeyboardArrowRight />}
            >
              Next
            </AppButton>
          ) : isAppointmentBookingPaymentEnabled && totalAmount > 0 ? (
            <>
              <AppButton
                sx={{ minWidth: 180 }}
                size="small"
                onClick={handleOpenPaymentModal}
                loading={updatingAppointment}
              >
                Proceed to Pay
              </AppButton>
              <PaymentConfirmationModal
                open={showPaymentModal}
                onClose={handleClosePaymentModal}
                onProceedToPay={handleProceedToPaymentFromModal}
                loading={isCreatingOrder}
              />
              <PaymentFailureModal
                open={showFailureModal}
                onClose={handleCloseFailureModal}
                errorMessage={
                  <>
                    Transaction failed!
                    <br />
                    Please try again later.
                  </>
                }
                showCloseButton={true}
                onRetry={handleProceedToPaymentFromModal}
              />
            </>
          ) : (
            <AppButton
              sx={{ minWidth: 180 }}
              size="small"
              onClick={handleClickConfirmBooking}
              endIcon={<BiCheck />}
              loading={updatingAppointment}
            >
              Confirm Booking
            </AppButton>
          )}
        </div>
      </form>
    </FormProvider>
  );
};

export default memo(BookConsultation);
