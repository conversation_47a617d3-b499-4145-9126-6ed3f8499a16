import React, { useCallback } from 'react';

import { useFormContext } from 'react-hook-form';

import { LifestyleMode } from '@/constants/emr/lifestyle';

import { FormField } from '@/types/emr/lifestyle/questionnaire';

import { fieldComponents } from './field-renderers';

interface RenderFieldsProps {
  fields: FormField[];
  namePrefix?: string;
  groupClassName?: string;
  fieldClassName?: string;
  readonly?: boolean;
  mode?: LifestyleMode;
  patientData?: any[];
  variant?: 'modal' | 'timeline';
}

const RenderFields: React.FC<RenderFieldsProps> = ({
  fields,
  namePrefix = '',
  groupClassName = 'space-y-6',
  fieldClassName = '',
  readonly,
  mode,
  patientData = [],
  variant,
}) => {
  const { control, watch, setValue } = useFormContext();

  const renderField = useCallback(
    (field: FormField, name: string, index: number) => {
      if (!field.type) {
        console.warn(`Field has no type defined:`, field);
        return null;
      }

      const FieldComponent = fieldComponents[field.type];
      if (!FieldComponent) {
        console.warn(`No renderer found for field type: ${field.type}`);
        return null;
      }

      const fieldName = field.type === 'table' ? name : `${name}.value`;

      return (
        <div key={field.id} className={fieldClassName}>
          <FieldComponent
            name={fieldName}
            field={field}
            control={control}
            watch={watch}
            setValue={setValue}
            readonly={readonly}
            fieldIndex={index}
            mode={mode}
            patientData={patientData}
            variant={variant}
          />
        </div>
      );
    },
    [
      control,
      watch,
      setValue,
      readonly,
      fieldClassName,
      mode,
      patientData,
      variant,
    ]
  );

  if (!fields || fields.length === 0) {
    return (
      <div className="p-4 text-center text-gray-500">No fields to display</div>
    );
  }

  return (
    <div className={`${groupClassName} ${readonly ? 'readonly-form' : ''}`}>
      {fields?.map((field, index) =>
        renderField(field, `${namePrefix}.${index}`, index)
      )}
    </div>
  );
};

export default RenderFields;
