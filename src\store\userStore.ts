import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

import { getDoctorProfile } from '@/query/emr/doctor-profile/personal-info';
import { reorderQueue } from '@/query/patient';

import {
  AppointmentStatus,
  PatientStatus,
} from '@/constants/mrd/manage-patient/consultation';
// Helper function to calculate queue counts based on actual queue data
const calculateQueueCounts = (queues: any[]) => {
  let completedCount = 0;
  let inQueueCount = 0;

  for (const appointment of queues) {
    if (
      appointment.status === AppointmentStatus.Awaiting ||
      appointment.status === AppointmentStatus.Booked
    ) {
      inQueueCount += 1;
    } else if (
      appointment.status === AppointmentStatus.Done ||
      appointment.status === AppointmentStatus.Cancelled
    ) {
      completedCount += 1;
    }
  }

  return {
    completed: completedCount,
    inQueue: inQueueCount,
  };
};

type StorageState = {
  queue: UserState['queue'];
  appointments: UserState['appointments'];
  currentAppointment: UserState['currentAppointment'];
  queueInfo: UserState['queueInfo'];
};

export type UserState = {
  name: string;
  data: any;
  permissions: string[];
  queue: {
    id: string;
    date: string;
    queues: Record<string, any>[];
  };
  currentAppointment: Record<string, any> | null;
  appointments: any[];
  queueInfo: {
    completed: number;
    inQueue: number;
  };
  queueFilter: {
    date: Date;
  };
  setUser: any;
  setUserData: any;
  setPermissions: (permissions: string[]) => void;
  setQueue: any;
  moveAppointmentToCurrent: (_queueItemId: string) => void;
  moveAppointmentToDone: (_queueItemId: string) => void;
  moveAppointmentToCancelled: (_queueItemId: string) => void;
  moveCurrentAppointmentToQueue: () => void;
  setQueueFilter: (_payload: Partial<{ date: Date }>) => void;
  setAppointments: (_appointments: any[]) => void;
  fetchUser: () => void;
  fetchUserPermissions: () => Promise<string[]>;
};

export const useUserStore = create(
  subscribeWithSelector<UserState>((set, get) => ({
    name: '',
    data: {},
    permissions: [],
    queue: {
      id: '',
      doctorId: '',
      date: '',
      queues: [],
    },
    queueFilter: {
      date: new Date(),
    },
    currentAppointment: null,
    appointments: [],
    queueInfo: {
      completed: 0,
      inQueue: 0,
    },
    setUser: (name: string) => set({ name }),
    setUserData: (data: any) => {
      let user = data;
      if (Array.isArray(data)) {
        user = data[0] || {};
      }
      set({ data: user });
    },
    setPermissions: (permissions: string[]) => set({ permissions }),
    setQueue: (newQueue: any) =>
      set((state) => {
        if (!newQueue) {
          const newState = {
            ...state,
            currentAppointment: null,
            queueInfo: {
              inQueue: 0,
              completed: 0,
            },
            queue: {
              id: '',
              doctorId: '',
              date: '',
              queues: [],
            },
            appointments: [],
          };

          localStorage.setItem('userStore', JSON.stringify(newState));

          return newState;
        }

        const newCurrentAppointment = newQueue.queues.find((item: any) => {
          return item.status === AppointmentStatus.Consultation;
        });

        const newAppointments = newQueue.queues.filter((item: any) => {
          if (
            item.queueId !== newCurrentAppointment?.queueId &&
            (item.status === AppointmentStatus.Awaiting ||
              item.status === AppointmentStatus.Booked)
          ) {
            return true;
          }

          return false;
        });

        const queueCounts = calculateQueueCounts(newQueue.queues);

        const newState = {
          ...state,
          queue: newQueue,
          appointments: newAppointments || [],
          currentAppointment: newCurrentAppointment || null,
          queueInfo: queueCounts,
        };

        localStorage.setItem(
          'userStore',
          JSON.stringify({
            queue: newState.queue,
            appointments: newState.appointments,
            currentAppointment: newState.currentAppointment,
            queueInfo: newState.queueInfo,
          })
        );

        return newState;
      }),
    moveAppointmentToCurrent: (queueItemId: string) =>
      set((state) => {
        if (!state?.queue?.queues) {
          return state;
        }

        const newState = {
          ...state,
          queue: {
            ...state.queue,
          },
        };

        newState.appointments = state.appointments.filter(
          (item) => item.queueId !== queueItemId
        );

        newState.queue.queues = newState.queue.queues.map((item: any) => {
          if (item.queueId === queueItemId) {
            return {
              ...item,
              status: AppointmentStatus.Consultation,
            };
          }
          return item;
        });

        newState.currentAppointment =
          state.queue.queues.find((item) => item.queueId === queueItemId) ||
          null;

        if (newState.currentAppointment) {
          newState.currentAppointment.status = AppointmentStatus.Consultation;
        }

        // Recalculate queueInfo based on actual queue data
        newState.queueInfo = calculateQueueCounts(newState.queue.queues);

        localStorage.setItem(
          'userStore',
          JSON.stringify({
            queue: newState.queue,
            appointments: newState.appointments,
            currentAppointment: newState.currentAppointment,
            queueInfo: newState.queueInfo,
          })
        );

        return newState;
      }),
    moveAppointmentToDone: (queueItemId: string) =>
      set((state) => {
        if (!state.appointments) {
          return state;
        }

        const newState = {
          ...state,
        };

        newState.currentAppointment = null;
        newState.appointments = state.appointments.filter(
          (item) => item.queueId !== queueItemId
        );
        newState.queue.queues = newState.queue.queues.map((item: any) => {
          if (item.queueId === queueItemId) {
            return {
              ...item,
              status: AppointmentStatus.Done,
            };
          }

          return item;
        });

        // Recalculate queueInfo based on actual queue data
        newState.queueInfo = calculateQueueCounts(newState.queue.queues);

        return newState;
      }),
    moveAppointmentToCancelled: (queueItemId: string) =>
      set((state) => {
        if (!state.appointments) {
          return state;
        }

        const newState = {
          ...state,
        };

        newState.appointments = state.appointments.filter(
          (item) => item.queueId !== queueItemId
        );
        newState.queue.queues = newState.queue.queues.map((item: any) => {
          if (item.queueId === queueItemId) {
            return {
              ...item,
              status: AppointmentStatus.Cancelled,
              patientStatus: PatientStatus.NoShow,
            };
          }

          return item;
        });

        // Recalculate queueInfo based on actual queue data
        newState.queueInfo = calculateQueueCounts(newState.queue.queues);

        return newState;
      }),
    setQueueFilter(newFilter) {
      return set((state) => {
        const newState = {
          ...state,
        };

        newState.queueFilter = {
          ...newState.queueFilter,
          ...newFilter,
        };

        return newState;
      });
    },
    moveCurrentAppointmentToQueue: () => {
      set((state) => {
        if (!state?.currentAppointment) {
          return state;
        }

        const appointmentToMove = {
          ...state.currentAppointment,
          status: AppointmentStatus.Booked,
          // Set queue position to 1 (front of queue) when moving back to queue
          queuePosition: 1,
        };

        // Update the main queue.queues array with the moved appointment
        const updatedQueues = state.queue.queues.map((item: any) => {
          if (item.queueId === state.currentAppointment!.queueId) {
            return appointmentToMove;
          }
          // Increment queue position of all other appointments
          return {
            ...item,
            queuePosition: item.queuePosition + 1,
          };
        });

        // Add the moved appointment to the appointments array
        const appointments = [appointmentToMove, ...state.appointments].sort(
          (a, b) => a.queuePosition - b.queuePosition
        );

        const newState = {
          ...state,
          queue: {
            ...state.queue,
            queues: updatedQueues,
          },
          appointments: appointments,
          currentAppointment: null,
        };

        // Recalculate queueInfo based on actual queue data
        newState.queueInfo = calculateQueueCounts(newState.queue.queues);

        reorderQueue({ appointments });

        localStorage.setItem(
          'userStore',
          JSON.stringify({
            queue: newState.queue,
            appointments: newState.appointments,
            currentAppointment: newState.currentAppointment,
            queueInfo: newState.queueInfo,
          })
        );

        return newState;
      });
    },

    setAppointments: (appointments: any[]) =>
      set((state) => {
        const newState = {
          ...state,
          appointments: appointments,
        };
        return newState;
      }),

    fetchUser: async () => {
      try {
        const response = await getDoctorProfile();
        if (response?.data) {
          get().setUser(
            (Array.isArray(response.data)
              ? (response.data[0] as { name?: string })?.name
              : (response.data as { name?: string })?.name) ?? ''
          );
          get().setUserData(
            Array.isArray(response.data) ? response.data[0] : response.data
          );
        }
      } catch (error) {
        console.error(error);
      }
    },
    fetchUserPermissions: async (): Promise<string[]> => {
      try {
        const response = await getDoctorProfile();
        let permissions: string[] = [];

        if (response?.data) {
          const userData = Array.isArray(response.data) ? response.data[0] : response.data;
          
          if (userData.permissionKeys) {
            permissions = Array.isArray(userData.permissionKeys)
              ? userData.permissionKeys
              : [userData.permissionKeys];
          } else if (userData.permissions) {
            permissions = Array.isArray(userData.permissions)
              ? userData.permissions
              : [userData.permissions];
          }
          
          // Update permissions in the store
          get().setPermissions(permissions);
        }
        
        return permissions;
      } catch (error) {
        console.error('Error fetching user permissions:', error);
        throw error;
      }
    },
  }))
);

if (typeof window !== 'undefined') {
  window.addEventListener('storage', (e) => {
    if (e.key === 'userStore' && e.newValue) {
      const newState = JSON.parse(e.newValue) as StorageState;
      useUserStore.setState({
        queue: newState.queue,
        appointments: newState.appointments,
        currentAppointment: newState.currentAppointment,
        queueInfo: newState.queueInfo,
      });
    }
  });
}
