import { LifestyleRecordStatus } from '@/constants/emr/lifestyle';
import { LifestyleFields } from '@/constants/emr/lifestyle/lifestyle-fields';
import { LifestyleSources } from '@/constants/emr/lifestyle/lifestyle-sources';

export interface BaseField {
  id: string;
  label?: string;
  type: LifestyleFields;
  required?: boolean;
  disabled?: boolean;
  description?: string;
  value?: any;
  placeholder?: string;
  labelClass?: string;

  // For searchable select
  options?: Array<{ value: string; label: string }>;
  fetchOptions?: any; // Will be properly typed in specific field interfaces

  // For dependent fields
  dependsOn?: string; // Field ID this field depends on
  fetchDependentData?: any; // Will be properly typed in specific field interfaces
  fieldMapping?: Record<string, string>; // For mapping API response to form fields
}

export interface RadioField extends Omit<BaseField, 'options'> {
  type: 'radio';
  options: string[]; // Override the options type for radio fields
  allowOtherSpecify?: boolean;
  modal?: {
    title: string;
    inputType: 'text' | 'number';
  };
}

export interface SliderField extends BaseField {
  type: 'slider';
  min: number;
  max: number;
  step: number;
}

export interface NumberField extends BaseField {
  type: 'number';
  min?: number;
  max?: number;
}

export interface FetchOptionsConfig {
  endpoint: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  queryParam?: string;
  responseMapper?: {
    valuePath?: string;
    labelPath?: string;
  };
}

export type FetchOptionsFunction = (
  query: string
) => Promise<Array<{ value: string; label: string }>>;

export interface SearchableSelectField extends BaseField {
  type: 'searchable_select';
  options?: Array<{ value: string; label: string }>;
  placeholder?: string;
  fetchOptions?: FetchOptionsConfig | FetchOptionsFunction;
}

export interface FetchDependentDataConfig {
  endpoint: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  paramName?: string;
  fieldMapping?: Record<string, string>;
}

export type FetchDependentDataFunction = (
  selectedValue: string
) => Promise<Record<string, any>>;

export interface DependentAutofillField extends BaseField {
  type: 'dependent_autofill';
  dependsOn: string; // Field ID this field depends on
  fetchDependentData: FetchDependentDataConfig | FetchDependentDataFunction;
  fieldMapping: Record<string, string>; // Maps API response fields to form fields
}

export interface TextField extends BaseField {
  type: 'text';
}

export interface TextareaField extends BaseField {
  type: 'textarea';
  placeholder?: string;
}

export interface SectionField extends BaseField {
  type: 'section';
  fields?: FormField[];
}

export interface ConditionalOption {
  label?: string;
  subField?: NumberField | TextField | TextareaField;
}

export interface ConditionalField extends BaseField {
  type: 'conditional';
  conditions: ConditionalOption[];
}

export interface TableHeader {
  id: string;
  label: string;
  type:
    | 'text'
    | 'number'
    | 'select'
    | 'time_range'
    | 'icon'
    | 'conditional_select'
    | 'searchable_select'
    | 'dependent_autofill';
  options?: string[] | Record<string, string[]>;
  dependsOn?: string;
  min?: number;
}

export interface TimeRange {
  from: string;
  to: string;
}

export interface TableRow {
  [key: string]: string | number | TimeRange;
}

export interface TableField extends BaseField {
  type: 'table';
  headers: TableHeader[];
  defaultRows?: TableRow[];
}

export interface MealGroup {
  id: string;
  label: string;
  defaultRows?: TableRow[];
}

export interface GroupedTableRow {
  [key: string]: string | number | TimeRange;
}

export interface GroupedTableGroup {
  id: string;
  label: string;
  rows: GroupedTableRow[];
}

export interface GroupedTableField extends BaseField {
  type: 'grouped_table';
  headers: TableHeader[];
  groupBy: string;
  mealGroups: MealGroup[];
}

export interface TimeRangeField extends BaseField {
  type: 'time_range';
}

export interface FrequencyColumn {
  header: string;
  option: string[];
}

export interface FrequencyField extends BaseField {
  type: 'frequency';
  columns: FrequencyColumn[];
}

export type FormField =
  | RadioField
  | SliderField
  | NumberField
  | TextField
  | TextareaField
  | SectionField
  | ConditionalField
  | TableField
  | GroupedTableField
  | TimeRangeField
  | FrequencyField
  | SearchableSelectField
  | DependentAutofillField;

export interface FieldGroup {
  id: string;
  title: string;
  icon?: string;
  fields: FormField[];
}

export type Questionnaire = {
  source: LifestyleSources;
  questions: FieldGroup[];
};

export interface Doctor {
  id: string;
  name?: string;
  designation?: string;
  department?: string;
}

export type QuestionnaireResponse = Questionnaire & {
  id: string;
  created_on: string;
  updated_on?: string;
  doctorName?: string;
  doctorDesignation?: string;
  doctor?: Doctor;
  status?: LifestyleRecordStatus;
};
