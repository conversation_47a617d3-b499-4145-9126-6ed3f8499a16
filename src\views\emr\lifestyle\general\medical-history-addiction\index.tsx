'use client';

import React, { useEffect, useCallback, useState } from 'react';

import { useForm, useFieldArray, FormProvider } from 'react-hook-form';

import { useMedicalHistoryAddictionStore } from '@/store/emr/lifestyle/general/medical-history-addiction';

import { DiagnosisStatus } from '@/constants/emr/lifestyle/medical-history-addiction';

import AppButton from '@/core/components/app-button';
import AppIcon from '@/core/components/app-icon';
import Loader from '@/core/components/app-loaders/Loader';
import {
  defaultMedicalHistoryAddictionValues,
  MedicalHistoryAddictionForm,
} from '@/types/emr/lifestyle/medical-history-addiction';

import MedicalHistoryAddictionFields from './MedicalHistoryAddictionFields';
import MedicalHistoryAddictionView from './MedicalHistoryAddictionView';

const MedicalHistoryAddiction: React.FC = () => {
  const {
    saveMedicalHistory,
    updateMedicalHistory,
    getMedicalHistory,
    saving,
    loading,
    medicalHistory,
    clearMedicalHistory,
  } = useMedicalHistoryAddictionStore();

  // Set initial view mode based on medicalHistory.id
  const [isViewMode, setIsViewMode] = useState(() => !!medicalHistory?.id);
  const [originalData, setOriginalData] =
    useState<MedicalHistoryAddictionForm | null>(null);

  const methods = useForm<MedicalHistoryAddictionForm>({
    defaultValues: defaultMedicalHistoryAddictionValues,
  });

  // Store original data when medicalHistory is loaded
  useEffect(() => {
    if (medicalHistory) {
      setOriginalData(medicalHistory);
    }
  }, [medicalHistory]);

  const { control, handleSubmit, reset } = methods;

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'diagnosis',
  });

  const handleReset = useCallback(() => {
    if (originalData) {
      reset({
        diagnosis: originalData.diagnosis,
        smoking: originalData.smoking,
        alcohol: originalData.alcohol,
        tobacco: originalData.tobacco,
        drugs: originalData.drugs,
        nicotineDependenceTest: originalData.nicotineDependenceTest || {
          responses: {
            timeToFirstCigarette: '',
            findDifficult: '',
            whichCigarette: '',
            cigarettesPerDay: '',
            moreFrequentMorning: '',
            smokeWhenIll: '',
          },
          testDate: '',
        },
      });
    } else {
      reset(defaultMedicalHistoryAddictionValues);
    }
  }, [originalData, reset]);

  const onSubmit = useCallback(
    async (data: MedicalHistoryAddictionForm) => {
      try {
        if (medicalHistory?.id) {
          await updateMedicalHistory(data);
        } else {
          await saveMedicalHistory(data);
        }
        // Refresh the data from server to ensure we have latest
        await getMedicalHistory();
        setIsViewMode(true);
      } catch (error) {
        console.error('Failed to save medical history:', error);
      }
    },
    [
      medicalHistory?.id,
      updateMedicalHistory,
      saveMedicalHistory,
      getMedicalHistory,
    ]
  );

  const handleAddDiagnosis = useCallback(() => {
    append({
      diseaseName: '',
      yearOfDiagnosis: '',
      diagnosisDuration: '',
      status: DiagnosisStatus.ACTIVE,
      treatmentHistory: '',
    });
  }, [append]);

  const handleRemoveDiagnosis = useCallback(
    (index: number) => {
      if (fields.length > 1) {
        remove(index);
      }
    },
    [fields.length, remove]
  );

  useEffect(() => {
    getMedicalHistory();
  }, [getMedicalHistory]);

  useEffect(() => {
    handleReset();
  }, [handleReset]);

  useEffect(() => {
    return () => {
      clearMedicalHistory();
    };
  }, [clearMedicalHistory]);

  // Keep isViewMode in sync with medicalHistory.id and reset form when entering view mode
  useEffect(() => {
    const shouldBeInViewMode = !!medicalHistory?.id;
    setIsViewMode(shouldBeInViewMode);

    if (shouldBeInViewMode) {
      // Reset form with original data when entering view mode
      reset(medicalHistory);
      setOriginalData(medicalHistory);
    }
  }, [medicalHistory, reset]);

  const handleCancel = useCallback(() => {
    // Reset form to original data from server
    if (originalData) {
      reset(originalData);
    } else {
      reset(defaultMedicalHistoryAddictionValues);
    }
    setIsViewMode(true);
  }, [originalData, reset]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Loader />
      </div>
    );
  }

  return (
    <FormProvider {...methods}>
      {isViewMode ? (
        <div className="flex flex-col h-full">
          <MedicalHistoryAddictionView />
          <div className="p-4">
            <div className="flex justify-end">
              <AppButton
                variant="outlined"
                onClick={() => setIsViewMode(false)}
                endIcon={<AppIcon icon="carbon:edit" />}
                sx={{ minWidth: 150, height: 32 }}
              >
                Edit Details
              </AppButton>
            </div>
          </div>
        </div>
      ) : (
        <div className="flex flex-col h-full">
          <form
            onSubmit={handleSubmit(onSubmit)}
            className="flex flex-col h-full"
          >
            <MedicalHistoryAddictionFields
              fields={fields}
              onAdd={handleAddDiagnosis}
              onRemove={handleRemoveDiagnosis}
            />

            {/* Form Actions - Sticky Bottom */}
            <div
              className="flex justify-end space-x-3 p-6 
             sticky bottom-0 z-10 shadow-lg"
            >
              <AppButton
                type="button"
                variant="outlined"
                onClick={handleCancel}
                disabled={saving}
                sx={{ minWidth: 150, height: 32 }}
              >
                Cancel
              </AppButton>
              <AppButton
                type="submit"
                loading={saving}
                disabled={saving}
                sx={{ minWidth: 150, height: 32 }}
              >
                Save Changes
              </AppButton>
            </div>
          </form>
        </div>
      )}
    </FormProvider>
  );
};

export default MedicalHistoryAddiction;
