import React from 'react';

import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  <PERSON><PERSON><PERSON>s,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
} from 'recharts';

import { ChartDataPoint } from '@/query/emr/lifestyle/nutrition-dashboard';

import { ChartTitle, LegendItem } from './ChartTitle';
import { CustomBarCursor } from './CustomBarCursor';

import { calculateBarWidth } from './utils/chartUtils';

interface NutritionBarChartProps {
  data: ChartDataPoint[];
  dataKey: string;
  colors: {
    top: string;
    bottom: string;
  };
  title: string;
  unit?: string;
  height?: number;
  showLegend?: boolean;
  yAxisDomain?: [number, number];
}

const NutritionBarChart: React.FC<NutritionBarChartProps> = ({
  data,
  dataKey,
  colors,
  title,
  unit = '',
  height = 286,
  showLegend = false,
  yAxisDomain,
}) => {
  // Calculate max value for Y-axis domain
  const calculateMaxYValue = () => {
    if (yAxisDomain) return yAxisDomain[1];
    if (!data.length) return 10;
    const max = Math.max(...data.map((item) => Number(item[dataKey]) || 0));
    // Round up to nearest 10 for better Y-axis ticks
    return Math.ceil(max / 10) * 10 || 10;
  };

  // Calculate Y-axis ticks based on max value
  const yTicks = () => {
    const yDomainMax = calculateMaxYValue();
    const ticks = [];
    const step = yDomainMax / 5;
    for (let i = 0; i <= 5; i++) {
      ticks.push(Math.round(step * i));
    }
    return ticks;
  };

  const yDomainMax = calculateMaxYValue();
  const maxDataValue = Math.max(
    ...data.map((item) => item[dataKey] as number),
    0
  );

  // Custom tick component for X-axis
  const CustomizedAxisTick = (props: any) => {
    const { x, y, payload } = props;
    const date = new Date(payload.value);
    const day = date.getDate();
    const month = date.toLocaleString('en-US', { month: 'short' });

    return (
      <g transform={`translate(${x},${y})`}>
        <text
          x={0}
          y={0}
          dy={12}
          textAnchor="middle"
          fill="#6b7280"
          fontSize={11}
        >
          {day}
        </text>
        <text
          x={0}
          y={12}
          dy={12}
          textAnchor="middle"
          fill="#6b7280"
          fontSize={9}
        >
          {month}
        </text>
      </g>
    );
  };

  const formatTooltipLabel = (label: string) => {
    const date = new Date(label);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="text-sm font-medium text-gray-900">
            {formatTooltipLabel(label)}
          </p>
          <p className="text-sm text-gray-600">
            <span className="font-medium" style={{ color: colors.top }}>
              {title}:
            </span>{' '}
            {payload[0].value.toFixed(1)} {unit}
          </p>
        </div>
      );
    }
    return null;
  };

  // Create gradient definition
  const gradientId = `gradient-${dataKey}`;

  // Custom cursor component with dynamic width
  const CustomCursor = (props: any) => (
    <CustomBarCursor
      {...props}
      barWidth={calculateBarWidth({
        dataLength: data.length,
        maxBarWidth: 30,
        margin: 40,
      })}
    />
  );

  return (
    <div className="w-full flex flex-col" style={{ height: `${height}px` }}>
      <ChartTitle
        title={`${title}`}
        className="px-4 pt-3 pb-3"
        rightContent={
          showLegend ? (
            <div className="flex items-center">
              <LegendItem
                color={colors.top}
                label={title}
                className="text-xs"
              />
            </div>
          ) : null
        }
      />
      <div className="flex-1">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={data}
            margin={{
              top: 40,
              right: 20,
              left: 25,
              bottom: 5,
            }}
          >
            <defs>
              <linearGradient id={gradientId} x1="0" y1="0" x2="0" y2="1">
                <stop offset="0%" stopColor={colors.top} />
                <stop offset="100%" stopColor={colors.bottom} />
              </linearGradient>
            </defs>
            <CartesianGrid
              strokeDasharray="3 3"
              stroke="#e5e7eb"
              horizontal={true}
              vertical={true}
            />
            <XAxis
              dataKey="date"
              axisLine={false}
              tickLine={false}
              interval={0}
              minTickGap={1}
              height={50}
              tick={<CustomizedAxisTick />}
              tickMargin={10}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tick={{ fill: '#6b7280', fontSize: 11 }}
              ticks={yTicks()}
              domain={[0, yDomainMax]}
              width={40}
            />
            <Tooltip
              contentStyle={{
                backgroundColor: 'white',
                border: '1px solid #e5e7eb',
                borderRadius: '0.5rem',
                boxShadow:
                  '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
              }}
              formatter={(value: number) => [
                `${Math.round(value * 10) / 10} ${unit}`,
                title,
              ]}
              labelFormatter={(label) =>
                new Date(label).toLocaleDateString('en-US', {
                  month: 'short',
                  day: 'numeric',
                })
              }
              cursor={<CustomCursor />}
            />
            <Bar
              dataKey={(data) => Math.min(data[dataKey], yDomainMax)}
              fill={`url(#${gradientId})`}
              name={title}
              radius={[4, 4, 0, 0]}
              maxBarSize={30}
              className="transition-all duration-200 hover:opacity-90"
              style={{
                cursor: 'pointer',
                transition: 'all 0.2s ease-in-out',
              }}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default NutritionBarChart;
