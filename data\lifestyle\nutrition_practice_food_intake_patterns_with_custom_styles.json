{"source": "nutrition_practice_food_intake_patterns", "questions": [{"id": "food_intake", "title": "Food intake patterns", "icon": "material-symbols:dining-outline", "fields": [{"id": "major_meals_per_day", "label": "Number of major meals per day", "type": "radio", "options": ["1", "2", "3", "4", "Other"], "allowOtherSpecify": true, "modal": {"title": "Meals per day", "inputType": "number"}, "customStyles": {"wrapperClassName": "border-b border-gray-300 pb-4 mb-4", "gap": 3, "labelMinWidth": 350, "labelMaxWidth": 350, "labelFontWeight": "400", "labelPaddingRight": "16px", "labelWhiteSpace": "nowrap", "radioGroupGap": "16px", "radioGroupFlexWrap": "nowrap", "radioLabelMargin": "0 16px 0 0", "radioLabelFontSize": "14px", "radioLabelLineHeight": "1.4"}}, {"id": "snacks_per_day", "label": "Number of snacks per day", "type": "radio", "options": ["1", "2", "3", "4", "5"], "customStyles": {"wrapperClassName": "border-b border-gray-300 pb-4 mb-4", "gap": 3, "labelMinWidth": 350, "labelMaxWidth": 350, "labelFontWeight": "400", "labelPaddingRight": "16px", "labelWhiteSpace": "nowrap", "radioGroupGap": "16px", "radioGroupFlexWrap": "nowrap", "radioLabelMargin": "0 16px 0 0", "radioLabelFontSize": "14px", "radioLabelLineHeight": "1.4"}}, {"id": "missed_meal", "label": "Which meal do you skip/miss most often in a day?", "type": "radio", "options": ["Breakfast", "Lunch", "Dinner", "Others"], "allowOtherSpecify": true, "modal": {"title": "Meal Type", "inputType": "text"}, "customStyles": {"wrapperClassName": "border-b border-gray-300 pb-4 mb-4", "gap": 3, "labelMinWidth": 350, "labelMaxWidth": 350, "labelFontWeight": "400", "labelPaddingRight": "16px", "labelWhiteSpace": "nowrap", "radioGroupGap": "16px", "radioGroupFlexWrap": "nowrap", "radioLabelMargin": "0 16px 0 0", "radioLabelFontSize": "14px", "radioLabelLineHeight": "1.4"}}, {"id": "family_meals", "label": "How many meals do you eat with family in a day?", "type": "radio", "options": ["0", "1", "2", "3", "4"], "customStyles": {"wrapperClassName": "border-b border-gray-300 pb-4 mb-4", "gap": 3, "labelMinWidth": 350, "labelMaxWidth": 350, "labelFontWeight": "400", "labelPaddingRight": "16px", "labelWhiteSpace": "nowrap", "radioGroupGap": "16px", "radioGroupFlexWrap": "nowrap", "radioLabelMargin": "0 16px 0 0", "radioLabelFontSize": "14px", "radioLabelLineHeight": "1.4"}}, {"id": "tv_meals", "label": "How many meals do you eat while watching TV in a day?", "type": "radio", "options": ["0", "1", "2", "3", "4"], "customStyles": {"wrapperClassName": "border-b border-gray-300 pb-4 mb-4", "gap": 3, "labelMinWidth": 350, "labelMaxWidth": 350, "labelFontWeight": "400", "labelPaddingRight": "16px", "labelWhiteSpace": "nowrap", "radioGroupGap": "16px", "radioGroupFlexWrap": "nowrap", "radioLabelMargin": "0 16px 0 0", "radioLabelFontSize": "14px", "radioLabelLineHeight": "1.4"}}, {"id": "taste_rating", "label": "How do you rate the taste of your meals?", "type": "slider", "min": 0, "max": 10, "step": 1, "description": "(0=Unpleasant to 10=Delicious)", "customStyles": {"wrapperClassName": "border-b border-gray-300 pb-4 mb-4"}}, {"id": "satiety_rating", "label": "How do you rate the satiety of your meals?", "type": "slider", "min": 0, "max": 10, "step": 1, "description": "(0=Not filling to 10=Overly full)", "customStyles": {"wrapperClassName": "border-b border-gray-300 pb-4 mb-4"}}]}, {"id": "beverage_intake", "title": "Beverage intake patterns", "icon": "material-symbols:coffee-outline-rounded", "fields": [{"id": "tea", "label": "Tea", "type": "conditional", "conditions": [{"label": "Yes", "subField": {"id": "tea_cups_per_day", "label": "Cups per Day", "type": "number", "min": 1, "max": 10}}, {"label": "No"}], "customStyles": {"wrapperClassName": "border-b border-gray-300 pb-4 mb-4", "gap": 3, "labelMinWidth": 200, "labelFontWeight": "400", "labelPaddingRight": "16px", "radioGroupGap": "16px", "radioLabelMargin": "0 16px 0 0"}}, {"id": "coffee", "label": "Coffee", "type": "conditional", "conditions": [{"label": "Yes", "subField": {"id": "coffee_cups_per_day", "label": "Cups per Day", "type": "number", "min": 1, "max": 10}}, {"label": "No"}], "customStyles": {"wrapperClassName": "border-b border-gray-300 pb-4 mb-4", "gap": 3, "labelMinWidth": 200, "labelFontWeight": "400", "labelPaddingRight": "16px", "radioGroupGap": "16px", "radioLabelMargin": "0 16px 0 0"}}]}]}