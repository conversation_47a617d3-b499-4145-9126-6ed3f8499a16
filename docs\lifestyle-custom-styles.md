# Lifestyle Form Custom Styles Documentation

## Overview

The lifestyle form field renderers now support custom styling through a `customStyles` property in the field definition. This allows you to apply specific styles to individual fields without affecting other forms or questions.

## How It Works

### 1. Field-Level Custom Styles

Add a `customStyles` object to any field in your question JSON:

```json
{
  "id": "major_meals_per_day",
  "label": "Number of major meals per day",
  "type": "radio",
  "options": ["1", "2", "3", "4", "Other"],
  "customStyles": {
    "wrapperClassName": "border-b border-gray-300 pb-4 mb-4",
    "gap": 3,
    "labelMinWidth": 350,
    "labelMaxWidth": 350,
    "labelFontWeight": "400",
    "labelPaddingRight": "16px",
    "labelWhiteSpace": "nowrap",
    "radioGroupGap": "16px",
    "radioGroupFlexWrap": "nowrap",
    "radioLabelMargin": "0 16px 0 0",
    "radioLabelFontSize": "14px",
    "radioLabelLineHeight": "1.4"
  }
}
```

### 2. Supported Field Types

Custom styles are supported for:
- **Radio Fields** (`type: "radio"`)
- **Slider Fields** (`type: "slider"`)
- **Conditional Fields** (`type: "conditional"`)

## Available Style Properties

### Wrapper Styles
- `wrapperClassName`: CSS class name for the field wrapper
- `wrapperStyles`: Inline styles object for the field wrapper

### Radio Field Styles
- `gap`: Gap between label and radio group (number)
- `labelMinWidth`: Minimum width of the label (number in px)
- `labelMaxWidth`: Maximum width of the label (number in px or 'none')
- `labelFontSize`: Font size of the label (string, e.g., '16px')
- `labelLineHeight`: Line height of the label (string, e.g., '1.4')
- `labelFontWeight`: Font weight of the label (string, e.g., '400')
- `labelMarginBottom`: Bottom margin of the label (string, e.g., '0px')
- `labelPaddingRight`: Right padding of the label (string, e.g., '16px')
- `labelWhiteSpace`: White space handling for label (string, e.g., 'nowrap')
- `labelStyles`: Additional label styles object
- `radioGroupGap`: Gap between radio options (string, e.g., '16px')
- `radioGroupFlexWrap`: Flex wrap for radio group (string, e.g., 'nowrap')
- `radioGroupStyles`: Additional radio group styles object
- `radioLabelMargin`: Margin for individual radio labels (string, e.g., '0 16px 0 0')
- `radioLabelFontSize`: Font size for radio option labels (string, e.g., '14px')
- `radioLabelLineHeight`: Line height for radio option labels (string, e.g., '1.4')
- `radioLabelStyles`: Additional radio label styles object
- `radioControlStyles`: Additional radio control styles object
- `containerStyles`: Additional container styles object

### Conditional Field Styles
Same as radio field styles, plus:
- All radio field properties apply to conditional fields

### Slider Field Styles
- `wrapperClassName`: CSS class name for the slider wrapper
- `wrapperStyles`: Inline styles object for the slider wrapper

## Example Usage

### Food Intake Pattern Styling (Figma Design)

```json
{
  "id": "major_meals_per_day",
  "label": "Number of major meals per day",
  "type": "radio",
  "options": ["1", "2", "3", "4", "Other"],
  "customStyles": {
    "wrapperClassName": "border-b border-gray-300 pb-4 mb-4",
    "gap": 3,
    "labelMinWidth": 350,
    "labelMaxWidth": 350,
    "labelFontWeight": "400",
    "labelPaddingRight": "16px",
    "labelWhiteSpace": "nowrap",
    "radioGroupGap": "16px",
    "radioGroupFlexWrap": "nowrap",
    "radioLabelMargin": "0 16px 0 0",
    "radioLabelFontSize": "14px",
    "radioLabelLineHeight": "1.4"
  }
}
```

### Compact Styling

```json
{
  "id": "quick_question",
  "label": "Quick question",
  "type": "radio",
  "options": ["Yes", "No"],
  "customStyles": {
    "gap": 1,
    "labelMinWidth": 200,
    "radioGroupGap": "8px",
    "radioLabelFontSize": "12px"
  }
}
```

### Custom Wrapper with Background

```json
{
  "id": "highlighted_question",
  "label": "Important question",
  "type": "radio",
  "options": ["Option 1", "Option 2"],
  "customStyles": {
    "wrapperClassName": "bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4",
    "labelFontWeight": "600"
  }
}
```

## Benefits

1. **Flexibility**: Each field can have its own unique styling
2. **Non-intrusive**: Only affects fields with `customStyles` defined
3. **Backward Compatible**: Existing forms continue to work without changes
4. **Future-proof**: Easy to add new style properties as needed
5. **Maintainable**: Styles are defined in the data, not hardcoded in components

## Migration from Hardcoded Styles

If you have existing forms with hardcoded styles, you can:

1. Keep the existing forms as-is (legacy support is maintained)
2. Gradually migrate to custom styles by adding `customStyles` to field definitions
3. Remove legacy style checks once all forms are migrated

## Best Practices

1. **Consistency**: Use consistent style values across similar questions
2. **Reusability**: Create style presets for common patterns
3. **Documentation**: Document your custom style patterns for team members
4. **Testing**: Test styles across different screen sizes and browsers
