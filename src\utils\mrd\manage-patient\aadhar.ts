export const formatAadhar = (value: string) => {
  const digits = value.replace(/\D/g, '');

  if (digits.length <= 4) {
    return digits;
  }

  if (digits.length <= 8) {
    return `${digits.slice(0, 4)}-${digits.slice(4)}`;
  }

  return `${digits.slice(0, 4)}-${digits.slice(4, 8)}-${digits.slice(8, 12)}`;
};

export const aadharValidationYup = (val?: string): boolean => {
  if (!val || val.trim() === '') return true; // Allow empty values (optional field)

  const sanitizedValue = val.replace(/-/g, '').trim();

  if (sanitizedValue.length === 12 && /^\d{12}$/.test(sanitizedValue)) {
    return true;
  }

  if (sanitizedValue.length < 12 && /^\d+$/.test(sanitizedValue)) {
    return true;
  }

  return false;
};
