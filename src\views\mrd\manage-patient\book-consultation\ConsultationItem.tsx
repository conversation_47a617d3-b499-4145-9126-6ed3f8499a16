import React, { memo, useCallback, useState } from 'react';

import { useFormContext } from 'react-hook-form';

import dayjs from 'dayjs';
import { FaPlus } from 'react-icons/fa';

import { useBookConsultationStore } from '@/store/mrd/manage-patient/book-consultation';
import { useManagePatientStore } from '@/store/mrd/manage-patient/manage';

import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';

import { Department } from '@/constants/mrd/manage-patient/consultation';

import ControlledDatePicker from '@/components/controlled-inputs/ControlledDatePicker';
import ControlledRadio from '@/components/controlled-inputs/ControlledRadio';
import ControlledSelectField from '@/components/controlled-inputs/ControlledSelectField';
import ControlledTimePicker from '@/components/controlled-inputs/ControlledTimePicker';

import AppButton from '@/core/components/app-button';
import DeleteModal from '@/core/components/delete-modal';
import {
  ConsultationForm,
  Doctors,
} from '@/types/mrd/manage-patient/consultation';

type Props = {
  index: number;
  onAdd: () => void;
  onDelete: () => void;
  queueId?: string;
  paymentStatus?: string | null;
};

const ConsultationItem = ({
  index,
  onAdd,
  onDelete,
  queueId,
  paymentStatus,
}: Props) => {
  const isPaid = paymentStatus === 'PAID';
  const { doctors, deleteAppointment, getAppointmentType } =
    useBookConsultationStore();
  const { patient } = useManagePatientStore();

  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const { control, setValue, watch } = useFormContext<{
    consultation: ConsultationForm[];
  }>();

  const doctor = watch(`consultation.${index}.doctorId`);
  const date = watch(`consultation.${index}.date`);
  const time = watch(`consultation.${index}.time`);

  const handleDeleteClick = () => {
    setIsDeleteModalOpen(true);
  };

  const handleChangeDoctor = useCallback(
    async (value: Doctors | Doctors[] | null) => {
      if (value === null) {
        return;
      }
      if (Array.isArray(value)) {
        value = value[0];
      }
      if (patient?.id && value?.id) {
        const type = await getAppointmentType(patient?.id, value?.id, '');
        setValue(`consultation.${index}.type`, type);
      }
    },
    [getAppointmentType, index, patient?.id, setValue]
  );

  const handleConfirmDelete = useCallback(async () => {
    try {
      setIsDeleting(true);
      if (queueId) {
        await deleteAppointment(queueId);
      }
    } finally {
      onDelete();
      setIsDeleting(false);
      setIsDeleteModalOpen(false);
    }
  }, [deleteAppointment, onDelete, queueId]);

  return (
    <>
      <div className="flex gap-base py-base border-t">
        <div className="flex gap-base w-[75%]">
          <ControlledSelectField
            name={`consultation.${index}.doctorId`}
            control={control}
            label="Select Doctor"
            placeholder="Select"
            options={doctors}
            getOptionLabel={(option) => option.name}
            getOptionValue={(option) => option.id}
            initiallyReadonly
            required
            isDisabled={isPaid}
            onChange={handleChangeDoctor}
          />
          <ControlledRadio
            name={`consultation.${index}.department`}
            control={control}
            label="Department"
            options={[
              { value: Department.InPatient, label: 'In Patient' },
              { value: Department.OutPatient, label: 'Out Patient' },
            ]}
            disabled={isPaid}
            formControlProps={{
              sx: { minWidth: 220 },
            }}
            required
          />
          <ControlledDatePicker
            label="Date"
            name={`consultation.${index}.date`}
            control={control}
            required
            minDate={dayjs()}
            disabled={isPaid}
          />
          <ControlledTimePicker
            label="Time"
            name={`consultation.${index}.time`}
            control={control}
            required
            disabled={isPaid}
          />
        </div>
        <div className="flex gap-base w-[25%] items-center pt-4 pl-3 justify-end">
          {index === 0 ? (
            <div className="flex gap-base items-center">
              <span className="text-xs">Book Another Appointment</span>
              <AppButton
                variant="outlined"
                size="large"
                sx={{ minWidth: 40, px: 1 }}
                onClick={onAdd}
              >
                <FaPlus />
              </AppButton>
            </div>
          ) : (
            <AppButton
              variant="text"
              size="small"
              color="error"
              onClick={handleDeleteClick}
              loading={isDeleting}
            >
              Delete
            </AppButton>
          )}
        </div>
      </div>
      <DeleteModal
        open={isDeleteModalOpen}
        onClose={() => !isDeleting && setIsDeleteModalOpen(false)}
        onDelete={handleConfirmDelete}
        isLoading={isDeleting}
        confirmationMessage="Are you sure you want to delete this appointment?"
        bodyContent={
          <div className="flex gap-base items-center">
            <span className="text-sm font-semibold text-blue-500">
              {doctor?.name ?? ''}
            </span>
            <div className="text-sm">
              <span>
                {`${formatDate(
                  date,
                  DateFormats.DATE_DD_MM_YYYY_SLASH
                )} | ${time}`}
              </span>
            </div>
          </div>
        }
      />
    </>
  );
};

export default memo(ConsultationItem);
