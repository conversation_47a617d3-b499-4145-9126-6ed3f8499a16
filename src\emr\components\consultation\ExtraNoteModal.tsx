import React, { useEffect, useState } from 'react';

import { Controller, useForm } from 'react-hook-form';
import { components as SelectComponents } from 'react-select';

import { Box, IconButton } from '@mui/material';

import EditableText from '@/lib/common/editable_text';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import {
  useDiagnosisStore,
  DiagnosisRecord,
} from '@/store/extraNoteDiagnosisStore';

import { searchMedicalCodes } from '@/query/medical-codes';

import { noteModes } from '@/utils/constants/consultation';
import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';
import { isHtmlContentEmpty } from '@/utils/textUtil';

import AppAsyncSearch from '@/core/components/app-async-search';
import AppIcon from '@/core/components/app-icon';
import AppModal from '@/core/components/app-modal';
import CustomModal from '@/core/components/modal';
import PrimaryButton from '@/core/components/primary-button';

import ActivityStatusSelector from './ActivityStatusSelector';
import CombinedStatusFilter from './CombinedStatusFilter';
import { StyledTypography } from './Common';
import DiagnosisEntry from './DiagnosisEntry';
import DiagnosisStatusSelector from './DiagnosisStatusSelector';

interface ExampleUsageProps {
  open: boolean;
  onClose: () => void;
  selectedTitle: string | null;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setShowCancelButton: React.Dispatch<React.SetStateAction<boolean>>;
  displayTitle?: string | null;
  showCancelButton?: boolean;
}

const { EDIT, VIEW } = noteModes;

interface FormData {
  content: string;
  diagnosisStatus: 'provisional' | 'confirmed';
  activityStatus: 'active' | 'inactive';
}

interface NewDiagnosisEntry {
  id: string;
  content: string;
  diagnosisStatus: 'provisional' | 'confirmed';
  activityStatus: 'active' | 'inactive';
}

type DiagnosisSearchOption = { label: string; value: string };

const DiagnosisNoteModal: React.FC<ExampleUsageProps> = ({
  open,
  onClose,
  selectedTitle,
  setOpen,
  setShowCancelButton,
}) => {
  const isDiagnosisInConsultation = selectedTitle === 'Diagnosis';

  if (isDiagnosisInConsultation) {
    return (
      <DiagnosisModal
        open={open}
        onClose={onClose}
        selectedTitle={selectedTitle}
        setOpen={setOpen}
        setShowCancelButton={setShowCancelButton}
      />
    );
  }

  return (
    <OriginalExtraNoteModal
      open={open}
      onClose={onClose}
      selectedTitle={selectedTitle}
      setOpen={setOpen}
      setShowCancelButton={setShowCancelButton}
    />
  );
};

// Advanced Diagnosis Modal Component
const DiagnosisModal: React.FC<ExampleUsageProps> = ({
  open,
  onClose,
  selectedTitle,
  setOpen,
  setShowCancelButton,
}) => {
  const { doctorProfile } = useDoctorStore();
  const { patient } = useCurrentPatientStore();
  const {
    addExtraNote,
    getExtraNotes,
    records,
    updateExtraNote,
    loading,
    diagnosisStatusFilter,
    activityStatusFilter,
    setDiagnosisStatusFilter,
    setActivityStatusFilter,
  } = useDiagnosisStore();

  const { control, handleSubmit, setValue } = useForm<FormData>({
    defaultValues: {
      content: '',
      diagnosisStatus: 'provisional',
      activityStatus: 'active',
    },
  });
  const [newDiagnosisEntries, setNewDiagnosisEntries] = useState<
    NewDiagnosisEntry[]
  >([]);
  const [combinedFilter, setCombinedFilter] = useState('all');
  const [editedRecords, setEditedRecords] = useState<{
    [key: string]: DiagnosisRecord;
  }>({});

  const [enterDiagnosisStatus, setEnterDiagnosisStatus] = useState<
    'provisional' | 'confirmed'
  >('provisional');
  const [enterActivityStatus, setEnterActivityStatus] = useState<
    'active' | 'inactive'
  >('active');

  useEffect(() => {
    setValue('diagnosisStatus', enterDiagnosisStatus);
    setValue('activityStatus', enterActivityStatus);
  }, [enterDiagnosisStatus, enterActivityStatus, setValue]);

  const hasValidContent = (record: DiagnosisRecord): boolean => {
    if (!record.content) return false;

    try {
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = record.content;
      const textContent = tempDiv.textContent || tempDiv.innerText || '';
      return textContent.trim().length > 0;
    } catch {
      return record.content.trim().length > 0;
    }
  };

  const filteredRecords =
    records?.records
      ?.filter(
        (record) => record.field === selectedTitle && hasValidContent(record)
      )
      ?.sort(
        (a, b) =>
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      ) || [];

  useEffect(() => {
    if (patient?.id) {
      setNewDiagnosisEntries([]);
      setEditedRecords({});

      getExtraNotes(
        patient.id,
        diagnosisStatusFilter !== 'all' ? diagnosisStatusFilter : undefined,
        activityStatusFilter !== 'all' ? activityStatusFilter : undefined
      );
    }
  }, [patient?.id, getExtraNotes, diagnosisStatusFilter, activityStatusFilter]);

  useEffect(() => {
    if (open && patient?.id) {
      setNewDiagnosisEntries([]);
      setEditedRecords({});

      getExtraNotes(
        patient.id,
        diagnosisStatusFilter !== 'all' ? diagnosisStatusFilter : undefined,
        activityStatusFilter !== 'all' ? activityStatusFilter : undefined
      );
    }
  }, [
    open,
    patient?.id,
    getExtraNotes,
    diagnosisStatusFilter,
    activityStatusFilter,
  ]);

  useEffect(() => {
    if (!open) {
      setNewDiagnosisEntries([]);
      setEditedRecords({});
      setValue('content', '');
      setCombinedFilter('all');
      setDiagnosisStatusFilter('all');
      setActivityStatusFilter('all');
    }
  }, [open, setValue, setDiagnosisStatusFilter, setActivityStatusFilter]);

  useEffect(() => {
    if (diagnosisStatusFilter === 'all' && activityStatusFilter === 'all') {
      setCombinedFilter('all');
    } else if (
      diagnosisStatusFilter === 'provisional' &&
      activityStatusFilter === 'all'
    ) {
      setCombinedFilter('provisional');
    } else if (
      diagnosisStatusFilter === 'confirmed' &&
      activityStatusFilter === 'all'
    ) {
      setCombinedFilter('confirmed');
    } else if (
      diagnosisStatusFilter === 'all' &&
      activityStatusFilter === 'active'
    ) {
      setCombinedFilter('active');
    } else if (
      diagnosisStatusFilter === 'all' &&
      activityStatusFilter === 'inactive'
    ) {
      setCombinedFilter('inactive');
    }
  }, [diagnosisStatusFilter, activityStatusFilter]);

  const onSubmit = async (data: FormData) => {
    try {
      const existingRecords = records?.records || [];

      if (isDiagnosisInConsultation) {
        // Diagnosis-specific logic
        const validNewDiagnosisEntries = newDiagnosisEntries.filter(
          (entry) => entry.content && entry.content.trim()
        );

        const newRecords = validNewDiagnosisEntries.map((entry, index) => {
          const record = {
            record_id: `R${Date.now()}_${index}`,
            field: selectedTitle,
            content: entry.content,
            doctor_id: doctorProfile?.id,
            timestamp: new Date().toISOString(),
            version: existingRecords.length + index + 1,
            status: 'active',
            diagnosisStatus: entry.diagnosisStatus || 'provisional',
            activityStatus: entry.activityStatus || 'active',
          };
          return record;
        });

        const manualTextRecords = [];
        if (data.content && data.content.trim()) {
          const manualRecord = {
            record_id: `R${Date.now()}_manual`,
            field: selectedTitle,
            content: data.content.trim(),
            doctor_id: doctorProfile?.id,
            timestamp: new Date().toISOString(),
            version: existingRecords.length + newRecords.length + 1,
            status: 'active',
            diagnosisStatus: data.diagnosisStatus || 'provisional',
            activityStatus: data.activityStatus || 'active',
          };
          manualTextRecords.push(manualRecord);
        }

        const updatedExistingRecords = existingRecords.map((record) => {
          const editedRecord = editedRecords[record.record_id];
          return editedRecord ? editedRecord : record;
        });

        const hasNewRecords =
          newRecords.length > 0 || manualTextRecords.length > 0;
        const hasModifiedRecords = Object.keys(editedRecords).length > 0;
        const isFirstDiagnosisForPatient = existingRecords.length === 0;

        if ((hasNewRecords || hasModifiedRecords) && !patient?.id) {
          console.error(
            'Cannot process diagnosis records: Patient ID is missing'
          );
          return;
        }

        if (isFirstDiagnosisForPatient && hasNewRecords) {
          const recordsToCreate = [...newRecords, ...manualTextRecords];

          await addExtraNote(patient?.id as string, {
            records: recordsToCreate,
          });
        } else if (
          !isFirstDiagnosisForPatient &&
          (hasNewRecords || hasModifiedRecords)
        ) {
          const allRecords = [
            ...updatedExistingRecords,
            ...newRecords,
            ...manualTextRecords,
          ];

          if (records?.id) {
            await updateExtraNote(records.id, {
              records: allRecords,
            });
          } else {
            console.error(
              'Cannot update diagnosis records: records.id is missing'
            );
          }
        }

        setNewDiagnosisEntries([]);
        setEditedRecords({});
        setShowCancelButton(false);
        setEnterDiagnosisStatus('provisional');
        setEnterActivityStatus('active');
        setValue('content', '');
        setValue('diagnosisStatus', 'provisional');
        setValue('activityStatus', 'active');
        setOpen(false);

        setDiagnosisStatusFilter('all');
        setActivityStatusFilter('all');
        setCombinedFilter('all');

        await getExtraNotes(patient?.id as string);
      } else {
        if (!data.content || !data.content.trim()) {
          return;
        }

        const simpleRecord = {
          record_id: `R${Date.now()}_simple`,
          field: selectedTitle,
          content: data.content.trim(),
          doctor_id: doctorProfile?.id,
          timestamp: new Date().toISOString(),
          version: existingRecords.length + 1,
          status: 'active',
          diagnosisStatus: 'provisional' as const,
          activityStatus: 'active' as const,
        };

        const isFirstRecordForPatient = existingRecords.length === 0;

        if (!patient?.id) {
          console.error('Cannot process records: Patient ID is missing');
          return;
        }

        if (isFirstRecordForPatient) {
          await addExtraNote(patient?.id as string, {
            records: [simpleRecord],
          });
        } else {
          const allRecords = [...existingRecords, simpleRecord];
          if (records?.id) {
            await updateExtraNote(records.id, {
              records: allRecords,
            });
          } else {
            console.error('Cannot update records: records.id is missing');
          }
        }

        setValue('content', '');
        setOpen(false);
        await getExtraNotes(patient?.id as string);
      }
    } catch (error) {
      console.error('Error submitting data:', error);
    }
  };

  const loadDiagnosisOptions = async (
    inputValue: string
  ): Promise<DiagnosisSearchOption[]> => {
    try {
      if (!inputValue || inputValue.trim().length < 2) return [];
      const results = await searchMedicalCodes(inputValue.trim());
      return results.map((text) => ({ label: text, value: text }));
    } catch (_e) {
      return [];
    }
  };

  const DropdownIndicator = (props: any) => (
    <SelectComponents.DropdownIndicator {...props}>
      <AppIcon icon="lucide:search" width={16} height={16} />
    </SelectComponents.DropdownIndicator>
  );

  const handleSelect = (opt: DiagnosisSearchOption | null) => {
    if (!opt) return;

    const newEntry: NewDiagnosisEntry = {
      id: `new_${Date.now()}`,
      content: opt.label,
      diagnosisStatus: enterDiagnosisStatus,
      activityStatus: enterActivityStatus,
    };

    setNewDiagnosisEntries((prev) => [...prev, newEntry]);
    setShowCancelButton(true);
  };

  const handleNewDiagnosisStatusChange = (
    entryId: string,
    field: 'diagnosisStatus' | 'activityStatus',
    value: 'provisional' | 'confirmed' | 'active' | 'inactive'
  ) => {
    setNewDiagnosisEntries((prev) =>
      prev.map((entry) =>
        entry.id === entryId ? { ...entry, [field]: value } : entry
      )
    );
  };

  const handleExistingDiagnosisStatusChange = (
    recordId: string,
    field: 'diagnosisStatus' | 'activityStatus',
    value: 'provisional' | 'confirmed' | 'active' | 'inactive'
  ) => {
    const originalRecord = filteredRecords.find(
      (r) => r.record_id === recordId
    );
    if (originalRecord) {
      setEditedRecords((prev) => ({
        ...prev,
        [recordId]: {
          ...originalRecord,
          ...prev[recordId],
          [field]: value,
        },
      }));
    }
  };

  const removeNewDiagnosis = (entryId: string) => {
    setNewDiagnosisEntries((prev) =>
      prev.filter((entry) => entry.id !== entryId)
    );
  };

  const handleCombinedFilterChange = async (option: any) => {
    setCombinedFilter(option.value);
    setDiagnosisStatusFilter(option.diagnosisStatus);
    setActivityStatusFilter(option.activityStatus);

    if (patient?.id) {
      await getExtraNotes(
        patient.id,
        option.diagnosisStatus !== 'all' ? option.diagnosisStatus : undefined,
        option.activityStatus !== 'all' ? option.activityStatus : undefined
      );
    }
  };

  const isDiagnosisInConsultation = selectedTitle === 'Diagnosis';

  return (
    <AppModal
      open={open}
      onClose={onClose}
      title=""
      classes={{
        root: 'w-[50vw] h-[70vh] flex flex-col',
        body: 'flex-1 overflow-hidden',
      }}
    >
      <Box
        component="form"
        onSubmit={handleSubmit(onSubmit)}
        sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}
      >
        {isDiagnosisInConsultation && (
          <Box sx={{ flexShrink: 0, mb: 1 }}>
            <Box mb={1}>
              <AppAsyncSearch<DiagnosisSearchOption>
                placeholder="Search by Disease/ICD Code"
                loadOptions={loadDiagnosisOptions}
                getOptionLabel={(o) => o.label}
                getOptionValue={(o) => o.value}
                defaultOptions={[]}
                components={{ DropdownIndicator }}
                onChange={handleSelect}
                onKeyDown={(e: any) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    e.stopPropagation();
                  }
                }}
              />
            </Box>
          </Box>
        )}

        {isDiagnosisInConsultation && (
          <Box
            sx={{
              flexShrink: 0,
              mb: 2,
              border: '1px solid #E0E6EB',
              borderRadius: '8px',
              padding: '10px',
              backgroundColor: '#fff',
            }}
          >
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                padding: '4px 0px 4px 0px',
                mb: newDiagnosisEntries.length > 0 ? 2 : 0,
              }}
            >
              <Box
                sx={{ display: 'flex', alignItems: 'center', gap: 2, flex: 1 }}
              >
                <Box sx={{ display: 'flex', gap: '10px' }}>
                  <DiagnosisStatusSelector
                    value={enterDiagnosisStatus}
                    onChange={(value) =>
                      setEnterDiagnosisStatus(
                        value as 'provisional' | 'confirmed'
                      )
                    }
                    className="h-6 text-sm min-w-[70px]"
                  />
                  <ActivityStatusSelector
                    value={enterActivityStatus}
                    onChange={(value) =>
                      setEnterActivityStatus(value as 'active' | 'inactive')
                    }
                    className="h-6 text-sm min-w-[55px]"
                  />
                </Box>
                <Box sx={{ flex: 1, ml: 2 }}>
                  <Controller
                    name="content"
                    control={control}
                    render={({ field }) => (
                      <input
                        {...field}
                        type="text"
                        placeholder=""
                        className="w-full text-sm text-black bg-transparent border-none outline-none"
                        style={{ fontSize: '14px', fontWeight: 'normal' }}
                      />
                    )}
                  />
                </Box>
              </Box>
            </Box>

            {newDiagnosisEntries.map((entry) => (
              <DiagnosisEntry
                key={entry.id}
                diagnosis={{
                  record_id: entry.id,
                  field: selectedTitle,
                  content: entry.content,
                  doctor_id: doctorProfile?.id,
                  timestamp: new Date().toISOString(),
                  version: 1,
                  status: 'active',
                  diagnosisStatus: entry.diagnosisStatus,
                  activityStatus: entry.activityStatus,
                }}
                onStatusChange={handleNewDiagnosisStatusChange}
                onRemove={removeNewDiagnosis}
                showRemoveButton={true}
                isEditable={true}
                isSavedRecord={false}
              />
            ))}
          </Box>
        )}

        {isDiagnosisInConsultation ? (
          <Box
            sx={{
              flex: 1,
              display: 'flex',
              flexDirection: 'column',
              mb: 2,
              minHeight: 0,
              border: '1px solid #E0E6EB',
              borderRadius: '8px',
              padding: '16px',
              backgroundColor: '#fff',
            }}
          >
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                mb: 2,
                flexShrink: 0,
              }}
            >
              <span
                className="text-black"
                style={{ fontSize: '16px', fontWeight: '600' }}
              >
                Diagnosis
              </span>
              <Box>
                <CombinedStatusFilter
                  value={combinedFilter}
                  onChange={handleCombinedFilterChange}
                  placeholder="All Status"
                  className="min-w-[140px]"
                />
              </Box>
            </Box>

            <Box
              sx={{
                flex: 1,
                overflowY: 'auto',
                minHeight: 0,
                maxHeight: '300px',
                '&::-webkit-scrollbar': {
                  width: '6px',
                },
                '&::-webkit-scrollbar-track': {
                  background: '#f1f1f1',
                  borderRadius: '3px',
                },
                '&::-webkit-scrollbar-thumb': {
                  background: '#c1c1c1',
                  borderRadius: '3px',
                },
                '&::-webkit-scrollbar-thumb:hover': {
                  background: '#a8a8a8',
                },
              }}
            >
              {filteredRecords.length > 0 ? (
                filteredRecords.map((record) => {
                  const recordWithDefaults = {
                    ...record,
                    diagnosisStatus: record.diagnosisStatus || 'provisional',
                    activityStatus: record.activityStatus || 'active',
                  };

                  const displayRecord =
                    editedRecords[record.record_id] || recordWithDefaults;

                  return (
                    <DiagnosisEntry
                      key={record.record_id}
                      diagnosis={displayRecord}
                      onStatusChange={handleExistingDiagnosisStatusChange}
                      showRemoveButton={false}
                      isEditable={true}
                      isSavedRecord={true}
                    />
                  );
                })
              ) : (
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    height: '100px',
                    color: '#666',
                    fontSize: '14px',
                  }}
                >
                  No data available
                </Box>
              )}
            </Box>
          </Box>
        ) : (
          <Box
            sx={{
              flex: 1,
              display: 'flex',
              flexDirection: 'column',
              mb: 2,
              minHeight: 0,
            }}
          >
            <Controller
              name="content"
              control={control}
              render={({ field }) => (
                <textarea
                  {...field}
                  placeholder={`Enter ${selectedTitle?.toLowerCase() || 'content'}...`}
                  className="w-full h-full text-sm text-black bg-transparent border border-gray-300 rounded p-3 outline-none resize-none"
                  style={{
                    fontSize: '14px',
                    fontWeight: 'normal',
                    minHeight: '200px',
                  }}
                />
              )}
            />
          </Box>
        )}

        <Box
          sx={{
            display: 'flex',
            gap: 2,
            justifyContent: 'flex-end',
            pt: 2,
            borderTop: '1px solid #ddd',
            flexShrink: 0,
          }}
        >
          <PrimaryButton
            onClick={() => {
              if (isDiagnosisInConsultation) {
                setNewDiagnosisEntries([]);
                setEditedRecords({});
                setShowCancelButton(false);
                setDiagnosisStatusFilter('all');
                setActivityStatusFilter('all');
                setCombinedFilter('all');
              }
              setValue('content', '');
              onClose();
            }}
            className="capitalize text-md !px-10 !py-[-2px] !bg-transparent !border !border-black !text-black hover:!bg-gray-50"
          >
            Cancel
          </PrimaryButton>

          <PrimaryButton
            type="submit"
            className="capitalize text-md !px-10 !py-[-2px]"
            isLoading={loading}
            disabled={
              isDiagnosisInConsultation
                ? newDiagnosisEntries.length === 0 &&
                  Object.keys(editedRecords).length === 0
                : false // For non-diagnosis, allow saving even if content is empty (will be handled in onSubmit)
            }
          >
            Save
          </PrimaryButton>
        </Box>
      </Box>
    </AppModal>
  );
};

const OriginalExtraNoteModal: React.FC<ExampleUsageProps> = ({
  open,
  onClose,
  selectedTitle,
  setOpen,
}) => {
  const { doctorProfile } = useDoctorStore();
  const { patient } = useCurrentPatientStore();
  const {
    addExtraNote,
    getExtraNotes,
    records,
    updateExtraNote,
    mode,
    setMode,
    selectedRecord,
    setSelectedRecord,
    loading,
  } = useDiagnosisStore();

  useEffect(() => {
    if (open && selectedTitle) {
      const filteredRecords =
        records?.records?.filter((record) => record.field === selectedTitle) ||
        [];
      const hasRecords = filteredRecords.length > 0;
      setMode(hasRecords ? VIEW : 'create');
    }
  }, [open, selectedTitle, records, setMode]);

  useEffect(() => {
    if (open && patient?.id) {
      getExtraNotes(patient.id);
    }
  }, [open, patient?.id, getExtraNotes]);

  const { control, handleSubmit, setValue } = useForm<{ content: string }>({
    defaultValues: { content: '' },
  });

  const filteredRecords =
    records?.records?.filter((record) => record.field === selectedTitle) || [];

  useEffect(() => {
    if (selectedRecord) {
      setValue('content', selectedRecord.content);
    }
  }, [selectedRecord, setValue]);

  const onSubmit = async (data: { content: string }) => {
    try {
      if (!data.content || !data.content.trim()) {
        return;
      }

      const existingRecords = records?.records || [];

      if (selectedRecord) {
        const updatedRecords = existingRecords.map((record) =>
          record.record_id === selectedRecord?.record_id
            ? { ...record, content: data.content.trim() }
            : record
        );

        if (records?.id) {
          await updateExtraNote(records.id, {
            records: updatedRecords,
          });
        }
      } else {
        const newRecord = {
          record_id: `R${Date.now()}`,
          field: selectedTitle,
          content: data.content.trim(),
          doctor_id: doctorProfile?.id,
          timestamp: new Date().toISOString(),
          version: existingRecords.length + 1,
          status: 'active',
          diagnosisStatus: 'provisional' as const,
          activityStatus: 'active' as const,
        };

        const updatedRecords = [...existingRecords, newRecord];

        if (records?.id) {
          await updateExtraNote(records.id, {
            records: updatedRecords,
          });
        } else {
          await addExtraNote(patient?.id as string, {
            records: updatedRecords,
          });
        }
      }

      setValue('content', '');
      setSelectedRecord(null);
      setMode(VIEW);
      setOpen(false);

      await getExtraNotes(patient?.id as string);
    } catch (error) {
      console.error('Error submitting data:', error);
    }
  };

  const handleEdit = (record: any) => {
    setSelectedRecord(record);
    setMode(EDIT);
  };

  return (
    <CustomModal
      open={open}
      onClose={onClose}
      title={selectedTitle || 'Summary'}
      minHeight="25vh"
      maxHeight="25vh"
      width="40vw"
      formProps={
        mode !== VIEW ? { onSubmit: handleSubmit(onSubmit) } : undefined
      }
      content={
        <>
          {filteredRecords.length > 0 &&
            mode === VIEW &&
            filteredRecords.map((record, index) => {
              const isEmpty = isHtmlContentEmpty(record.content);

              return !isEmpty ? (
                <Box
                  key={index}
                  mb={2}
                  sx={{
                    borderBottom: '1px solid #C2CDD6',
                    padding: '8px',
                  }}
                  className="rounded-thin-scrollbar"
                >
                  <Box
                    display="flex"
                    alignItems="center"
                    justifyContent="space-between"
                  >
                    <StyledTypography variant="caption">
                      {formatDate(
                        record.timestamp,
                        DateFormats.READABLE_DATE_TIME
                      )}
                    </StyledTypography>

                    <Box display="flex" alignItems="center" sx={{ gap: 1 }}>
                      <IconButton
                        onClick={() => handleEdit(record)}
                        sx={{
                          width: 25,
                          height: 25,
                          padding: 0,
                          '&:hover': {
                            backgroundColor: 'transparent',
                          },
                        }}
                      >
                        <AppIcon icon="lucide:pencil" width={14} height={14} />
                      </IconButton>
                    </Box>
                  </Box>

                  <EditableText
                    key={record.record_id}
                    defaultValue={record.content}
                    editable={false}
                    bg="white"
                    className="break-words whitespace-pre-wrap w-full"
                  />
                </Box>
              ) : null;
            })}

          {mode !== VIEW && (
            <>
              {selectedRecord && (
                <StyledTypography variant="caption">
                  {formatDate(
                    selectedRecord.timestamp,
                    DateFormats.READABLE_DATE_TIME
                  )}
                </StyledTypography>
              )}

              <Controller
                name="content"
                control={control}
                render={({ field }) => (
                  <EditableText
                    bg="white"
                    placeholder="Enter text"
                    defaultValue={selectedRecord?.content}
                    {...field}
                  />
                )}
              />
            </>
          )}
        </>
      }
      actions={
        mode === VIEW && filteredRecords.length > 0 ? (
          <PrimaryButton
            className="capitalize h-8 text-md"
            onClick={() => {
              setMode('create');
              setSelectedRecord(null);
            }}
          >
            Add New Note
          </PrimaryButton>
        ) : (
          <>
            <PrimaryButton
              onClick={() => setMode(VIEW)}
              className="capitalize text-md h-8 !bg-[#637D92]"
            >
              Cancel
            </PrimaryButton>

            <PrimaryButton
              type="submit"
              className="capitalize text-md h-8"
              isLoading={loading}
            >
              Save Changes
            </PrimaryButton>
          </>
        )
      }
    />
  );
};

export default DiagnosisNoteModal;
