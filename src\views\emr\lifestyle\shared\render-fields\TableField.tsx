import React from 'react';

import {
  Controller,
  useFieldArray,
  useWatch,
  Control,
  useFormContext,
} from 'react-hook-form';

import { Button, IconButton, MenuItem, Select, TextField } from '@mui/material';
import { IoIosArrowDown } from 'react-icons/io';
import { MdOutlineAdd, MdClose, MdEdit } from 'react-icons/md';

import { LifestyleMode } from '@/constants/emr/lifestyle';

import DeleteModal from '@/core/components/delete-modal';
import { accentColors } from '@/core/theme/colors';
import { lightColors } from '@/core/theme/colors/light-colors';
import { TableField as TableFieldType } from '@/types/emr/lifestyle/questionnaire';

import { FieldComponentProps } from './types';

const borderlessTheme = (baseTheme: any) => ({
  ...baseTheme,
  components: {
    ...baseTheme.components,
    MuiOutlinedInput: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-notchedOutline': {
            border: 'none !important',
            borderColor: 'transparent !important',
          },
          '&:hover .MuiOutlinedInput-notchedOutline': {
            border: 'none !important',
            borderColor: 'transparent !important',
          },
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            border: 'none !important',
            borderColor: 'transparent !important',
          },
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            '& fieldset': {
              border: 'none !important',
              borderColor: 'transparent !important',
            },
            '&:hover fieldset': {
              border: 'none !important',
              borderColor: 'transparent !important',
            },
            '&.Mui-focused fieldset': {
              border: 'none !important',
              borderColor: 'transparent !important',
            },
          },
        },
      },
    },
    MuiSelect: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-notchedOutline': {
            border: 'none !important',
            borderColor: 'transparent !important',
          },
        },
      },
    },
  },
});

interface ConditionalSelectProps {
  controllerField: any;
  header: any;
  name: string;
  rowIndex: number;
  control: Control<any>;
  readonly?: boolean;
  mode?: string;
}

const ConditionalSelect: React.FC<ConditionalSelectProps> = ({
  controllerField,
  header,
  name,
  rowIndex,
  control,
  readonly,
  mode,
}) => {
  const [selectOpen, setSelectOpen] = React.useState(false);

  const dependentValue = useWatch({
    control,
    name: header.dependsOn
      ? `${name}.value.${rowIndex}.${header.dependsOn}`
      : `${name}.value.${rowIndex}.nonexistent`,
  });

  const conditionalOptions = React.useMemo(() => {
    if (!header.dependsOn) return [];
    return typeof header.options === 'object' && !Array.isArray(header.options)
      ? header.options[dependentValue] || []
      : [];
  }, [header.dependsOn, header.options, dependentValue]);

  React.useEffect(() => {
    if (
      header.dependsOn &&
      dependentValue &&
      controllerField.value &&
      !conditionalOptions.includes(controllerField.value)
    ) {
      controllerField.onChange('');
    }
  }, [dependentValue, conditionalOptions, controllerField, header.dependsOn]);

  const actualDependentValue = header.dependsOn ? dependentValue : null;

  return (
    <Select
      {...controllerField}
      value={controllerField.value || ''}
      size="small"
      fullWidth
      variant="outlined"
      displayEmpty={mode !== LifestyleMode.VIEW}
      disabled={readonly || !actualDependentValue}
      open={selectOpen}
      onOpen={() => setSelectOpen(true)}
      onClose={() => setSelectOpen(false)}
      IconComponent={
        mode === LifestyleMode.VIEW
          ? () => null
          : () => (
              <IoIosArrowDown
                className="h-4 w-auto text-[#637D92] mr-2 cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  if (!readonly && actualDependentValue) {
                    setSelectOpen(!selectOpen);
                  }
                }}
              />
            )
      }
      MenuProps={{
        PaperProps: {
          style: {
            maxHeight: 5 * 48, // 6 options, each option is 48px high (default MUI MenuItem height)
            overflowY: 'auto',
          },
        },
      }}
      sx={{
        width: '100%',
        minWidth: '200px',
        '&&& .MuiInputBase-root': {
          backgroundColor: 'transparent !important',
          color: mode === LifestyleMode.VIEW ? '#000000 !important' : 'inherit',
          border: 'none !important',
          outline: 'none !important',
          borderColor: 'transparent !important',
          boxShadow: 'none !important',
          borderWidth: '0px !important',
          borderStyle: 'none !important',
          '&:before': {
            border: 'none !important',
            borderBottom: 'none !important',
            display: 'none !important',
          },
          '&:after': {
            border: 'none !important',
            borderBottom: 'none !important',
            display: 'none !important',
          },
          '&:hover': {
            backgroundColor: 'transparent !important',
            border: 'none !important',
            borderColor: 'transparent !important',
            '&:before': {
              border: 'none !important',
              borderBottom: 'none !important',
              display: 'none !important',
            },
          },
          '&.Mui-focused': {
            backgroundColor: 'transparent !important',
            border: 'none !important',
            borderColor: 'transparent !important',
            boxShadow: 'none !important',
            '&:after': {
              border: 'none !important',
              borderBottom: 'none !important',
              display: 'none !important',
            },
          },
          '&.Mui-disabled': {
            backgroundColor: 'transparent !important',
            color:
              mode === LifestyleMode.VIEW ? '#000000 !important' : 'inherit',
            border: 'none !important',
            borderColor: 'transparent !important',
          },
        },
        '&&& .MuiOutlinedInput-root': {
          backgroundColor: 'transparent !important',
          color: mode === LifestyleMode.VIEW ? '#000000 !important' : 'inherit',
          border: 'none !important',
          outline: 'none !important',
          borderColor: 'transparent !important',
          borderWidth: '0px !important',
          borderStyle: 'none !important',
          boxShadow: 'none !important',
          '&:hover': {
            backgroundColor: 'transparent !important',
            border: 'none !important',
            borderColor: 'transparent !important',
          },
          '&.Mui-focused': {
            backgroundColor: 'transparent !important',
            border: 'none !important',
            borderColor: 'transparent !important',
          },
          '&.Mui-disabled': {
            backgroundColor: 'transparent !important',
            color:
              mode === LifestyleMode.VIEW ? '#000000 !important' : 'inherit',
            border: 'none !important',
            borderColor: 'transparent !important',
          },
          '& fieldset': {
            border: 'none !important',
            outline: 'none !important',
            borderColor: 'transparent !important',
          },
          '&:hover fieldset': {
            border: 'none !important',
            outline: 'none !important',
            borderColor: 'transparent !important',
          },
          '&.Mui-focused fieldset': {
            border: 'none !important',
            outline: 'none !important',
            borderColor: 'transparent !important',
          },
          '& .MuiOutlinedInput-notchedOutline': {
            border: 'none !important',
            outline: 'none !important',
            display: 'none !important',
            borderColor: 'transparent !important',
          },
        },
        '& .MuiSelect-select': {
          backgroundColor: 'transparent !important',
          color: mode === LifestyleMode.VIEW ? '#000000 !important' : 'inherit',
          '&:focus': {
            backgroundColor: 'transparent !important',
            color:
              mode === LifestyleMode.VIEW ? '#000000 !important' : 'inherit',
          },
          '&.Mui-disabled': {
            backgroundColor: 'transparent !important',
            color:
              mode === LifestyleMode.VIEW ? '#000000 !important' : 'inherit',
            WebkitTextFillColor:
              mode === LifestyleMode.VIEW ? '#000000 !important' : 'inherit',
          },
        },
      }}
      renderValue={(selected) => {
        if (!selected && mode !== LifestyleMode.VIEW) {
          return (
            <span style={{ color: '#9ca3af' }}>
              {actualDependentValue ? 'Select' : 'Select activity'}
            </span>
          );
        }
        return selected || '';
      }}
    >
      {conditionalOptions.map((option: string) => (
        <MenuItem key={option} value={option}>
          {option}
        </MenuItem>
      ))}
    </Select>
  );
};

export const TableField: React.FC<
  FieldComponentProps & {
    patientData?: any[];
  }
> = ({ name, field, control, readonly, mode, patientData = [] }) => {
  const tableField = field as TableFieldType;
  const [hasInitialized, setHasInitialized] = React.useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = React.useState(false);
  const [rowToDelete, setRowToDelete] = React.useState<{
    index: number;
    rowData: any;
  } | null>(null);
  const [editableFields, setEditableFields] = React.useState<
    Record<string, boolean>
  >({});
  const [selectOpenStates, setSelectOpenStates] = React.useState<
    Record<string, boolean>
  >({});

  const { fields, append, remove } = useFieldArray({
    control,
    name: `${name}.value`,
  });

  const { getValues, watch } = useFormContext();

  const watchedValue = watch(`${name}.value`);

  const appendRef = React.useRef(append);
  appendRef.current = append;

  const toggleFieldEdit = (rowIndex: number, fieldId: string) => {
    const fieldKey = `${rowIndex}-${fieldId}`;
    setEditableFields((prev) => ({
      ...prev,
      [fieldKey]: !prev[fieldKey],
    }));
  };

  const isFieldEditable = (rowIndex: number, fieldId: string) => {
    const fieldKey = `${rowIndex}-${fieldId}`;
    return editableFields[fieldKey] || false;
  };

  const toggleSelectOpen = (rowIndex: number, fieldId: string) => {
    const selectKey = `${rowIndex}-${fieldId}`;
    setSelectOpenStates((prev) => ({
      ...prev,
      [selectKey]: !prev[selectKey],
    }));
  };

  const isSelectOpen = (rowIndex: number, fieldId: string) => {
    const selectKey = `${rowIndex}-${fieldId}`;
    return selectOpenStates[selectKey] || false;
  };

  const setSelectOpen = (rowIndex: number, fieldId: string, open: boolean) => {
    const selectKey = `${rowIndex}-${fieldId}`;
    setSelectOpenStates((prev) => ({
      ...prev,
      [selectKey]: open,
    }));
  };

  React.useEffect(() => {
    if (
      hasInitialized &&
      fields.length === 0 &&
      tableField.defaultRows &&
      tableField.defaultRows.length > 0
    ) {
      const currentFormValues = getValues();
      tableField.defaultRows.forEach((row, index) => {
        console.warn(`Re-adding default row ${index}:`, row);
        appendRef.current({ ...row });
      });
    }
  }, [
    fields.length,
    hasInitialized,
    tableField.defaultRows,
    name,
    getValues,
    watchedValue,
  ]);

  const addRow = () => {
    try {
      const newRow: Record<string, string> = {};
      tableField.headers.forEach((header) => {
        newRow[header.id] = '';
      });

      append(newRow);

      setTimeout(() => {
        const currentFormValues = getValues();
        const currentFieldValue = watch(`${name}.value`);
      }, 0);
      setTimeout(() => {
        const currentFieldValue = watch(`${name}.value`);
      }, 100);
    } catch (error) {
      console.error('Error during add row:', error);
    }
  };

  const handleDeleteClick = (index: number) => {
    const rowData = fields[index];
    setRowToDelete({ index, rowData });
    setDeleteModalOpen(true);
  };

  const handleConfirmDelete = () => {
    if (rowToDelete !== null) {
      if (rowToDelete.index >= 0 && rowToDelete.index < fields.length) {
        try {
          remove(rowToDelete.index);
        } catch (error) {
          console.error('Error during deletion:', error);
        }
      } else {
        console.error(
          'Invalid row index for deletion:',
          rowToDelete.index,
          'fields.length:',
          fields.length
        );
      }

      setDeleteModalOpen(false);
      setRowToDelete(null);
    }
  };

  const handleCancelDelete = () => {
    setDeleteModalOpen(false);
    setRowToDelete(null);
  };

  const getActivityType = () => {
    if (!rowToDelete?.rowData) return '';

    const rowData = rowToDelete.rowData;

    if (rowData.activity_type) return rowData.activity_type;

    return '';
  };

  const getActivity = () => {
    if (!rowToDelete?.rowData) return '';

    const rowData = rowToDelete.rowData;

    if (rowData.activity) return rowData.activity;

    return '';
  };

  React.useEffect(() => {
    if (!hasInitialized) {
      if (
        fields.length === 0 &&
        tableField.defaultRows &&
        tableField.defaultRows.length > 0
      ) {
        const defaultRowsCopy = [...tableField.defaultRows];
        defaultRowsCopy.forEach((row) => {
          appendRef.current({ ...row });
        });
      }
      setHasInitialized(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Empty dependency array - only run once on mount

  return (
    <>
      <style jsx global>{`
        .exercise-patterns-table .MuiInputBase-root,
        .exercise-patterns-table .MuiOutlinedInput-root,
        .exercise-patterns-table .MuiSelect-root,
        .exercise-patterns-table .MuiFormControl-root,
        .exercise-patterns-table .MuiTextField-root {
          border: none !important;
          outline: none !important;
          border-color: transparent !important;
          box-shadow: none !important;
          border-width: 0 !important;
          border-style: none !important;
        }
        .exercise-patterns-table .MuiInputBase-root:hover,
        .exercise-patterns-table .MuiOutlinedInput-root:hover,
        .exercise-patterns-table .MuiSelect-root:hover,
        .exercise-patterns-table .MuiFormControl-root:hover,
        .exercise-patterns-table .MuiTextField-root:hover {
          border: none !important;
          border-color: transparent !important;
          border-width: 0 !important;
          border-style: none !important;
        }
        .exercise-patterns-table .MuiInputBase-root.Mui-focused,
        .exercise-patterns-table .MuiOutlinedInput-root.Mui-focused,
        .exercise-patterns-table .MuiSelect-root.Mui-focused,
        .exercise-patterns-table .MuiFormControl-root.Mui-focused,
        .exercise-patterns-table .MuiTextField-root.Mui-focused {
          border: none !important;
          border-color: transparent !important;
          box-shadow: none !important;
          border-width: 0 !important;
          border-style: none !important;
        }
        .exercise-patterns-table .MuiOutlinedInput-notchedOutline,
        .exercise-patterns-table fieldset,
        .exercise-patterns-table .MuiOutlinedInput-root fieldset,
        .exercise-patterns-table .MuiInputBase-root fieldset {
          border: none !important;
          border-color: transparent !important;
          display: none !important;
          opacity: 0 !important;
          border-width: 0 !important;
          border-style: none !important;
          visibility: hidden !important;
        }
        .exercise-patterns-table
          .MuiInputBase-root:hover
          .MuiOutlinedInput-notchedOutline,
        .exercise-patterns-table
          .MuiInputBase-root.Mui-focused
          .MuiOutlinedInput-notchedOutline,
        .exercise-patterns-table
          .MuiOutlinedInput-root:hover
          .MuiOutlinedInput-notchedOutline,
        .exercise-patterns-table
          .MuiOutlinedInput-root.Mui-focused
          .MuiOutlinedInput-notchedOutline {
          border: none !important;
          border-color: transparent !important;
          display: none !important;
          opacity: 0 !important;
          border-width: 0 !important;
          border-style: none !important;
          visibility: hidden !important;
        }
        .exercise-patterns-table .MuiSelect-select,
        .exercise-patterns-table .MuiInputBase-input {
          border: none !important;
          outline: none !important;
          border-width: 0 !important;
          border-style: none !important;
        }
        /* Additional specific targeting for stubborn borders - only input elements */
        .exercise-patterns-table [class*='MuiOutlinedInput'],
        .exercise-patterns-table [class*='MuiInputBase'],
        .exercise-patterns-table [class*='MuiSelect'] {
          border: none !important;
          border-width: 0 !important;
          border-style: none !important;
          border-color: transparent !important;
        }
        /* Ensure table styling works properly */
        .exercise-patterns-table table {
          border-collapse: collapse !important;
        }
        /* Remove only header borders, keep body borders */
        .exercise-patterns-table thead th {
          border-top: none !important;
          border-left: none !important;
          border-right: none !important;
        }
        /* Remove the top border of the table that creates sharp edges on header */
        .exercise-patterns-table table {
          border-top: none !important;
        }
        /* Add horizontal borders between rows with higher specificity */
        .exercise-patterns-table table tbody tr {
          border-bottom: 1px solid #d1d5db !important;
        }
        .exercise-patterns-table table tbody tr.border-b {
          border-bottom: 1px solid #d1d5db !important;
        }
        .exercise-patterns-table table tbody td {
          border-right: 1px solid #d1d5db !important;
        }
        .exercise-patterns-table table tbody td:last-child {
          border-right: none !important;
        }
      `}</style>
      <div className=" exercise-patterns-table mr-2">
        <div className={mode === LifestyleMode.VIEW ? '' : 'overflow-x-auto'}>
          <table
            className="min-w-full border border-gray-300"
            style={{
              borderCollapse: 'collapse',
            }}
          >
            <thead
              className={`text-white ${
                mode === LifestyleMode.VIEW ? 'bg-gray-400' : ''
              }`}
              style={{
                backgroundColor:
                  mode === LifestyleMode.VIEW ? undefined : 'hsl(200 97% 25%)',
                color: '#ffffff',
              }}
            >
              <tr>
                {tableField.headers.map((header) => {
                  const getColumnWidth = () => {
                    if (header.type === 'number') return 'w-16';
                    if (
                      header.type === 'conditional_select' ||
                      header.id === 'activity'
                    )
                      return 'w-48';
                    return 'w-24';
                  };

                  return (
                    <th
                      key={header.id}
                      className={`px-2 py-2 text-left text-sm font-medium ${getColumnWidth()}`}
                    >
                      {header.label}
                    </th>
                  );
                })}
                {!readonly && (
                  <th className="px-2 py-2 text-center text-sm font-medium w-12">
                    Actions
                  </th>
                )}
              </tr>
            </thead>
            <tbody>
              {!readonly && (
                <tr>
                  <td
                    colSpan={tableField.headers.length + 1}
                    className="py-4 text-center border-b border-gray-200"
                  >
                    <div className="flex justify-center items-center gap-2">
                      <span className="font-medium text-lg">Add Entry</span>
                      <Button
                        variant="outlined"
                        onClick={addRow}
                        size="small"
                        sx={{
                          minWidth: 0,
                          padding: '2px 4px',
                          borderColor: accentColors.blue,
                          '&:hover': {
                            borderColor: accentColors.blue,
                            backgroundColor: '#e0f2fe',
                          },
                        }}
                      >
                        <MdOutlineAdd
                          size={24}
                          style={{
                            fontWeight: 'bold',
                            color: lightColors.secondary,
                          }}
                        />
                      </Button>
                    </div>
                  </td>
                </tr>
              )}
              {fields.map((row, rowIndex) => (
                <tr
                  key={row.id}
                  className={`border-b border-gray-200 ${
                    mode === LifestyleMode.VIEW ? 'bg-transparent' : ''
                  }`}
                >
                  {tableField.headers.map((header) => {
                    const getColumnWidth = () => {
                      if (header.type === 'number') return 'w-16';
                      if (
                        header.type === 'conditional_select' ||
                        header.id === 'activity'
                      )
                        return 'w-48';
                      return 'w-24';
                    };

                    return (
                      <td
                        key={header.id}
                        className={`${mode === LifestyleMode.VIEW ? 'px-2' : 'px-4'} py-2 border-r border-gray-200 last:border-r-0 ${getColumnWidth()} ${
                          mode === LifestyleMode.VIEW
                            ? 'bg-transparent text-black'
                            : ''
                        }`}
                      >
                        <Controller
                          name={`${name}.value.${rowIndex}.${header.id}`}
                          control={control}
                          defaultValue=""
                          render={({ field: controllerField }) => {
                            if (
                              header.type === 'conditional_select' &&
                              header.options &&
                              header.dependsOn
                            ) {
                              return (
                                <ConditionalSelect
                                  controllerField={controllerField}
                                  header={header}
                                  name={name}
                                  rowIndex={rowIndex}
                                  control={control}
                                  readonly={readonly}
                                  mode={mode}
                                />
                              );
                            } else if (
                              header.type === 'select' &&
                              header.options
                            ) {
                              const selectOptions = Array.isArray(
                                header.options
                              )
                                ? header.options
                                : [];
                              return (
                                <Select
                                  {...controllerField}
                                  value={controllerField.value || ''}
                                  size="small"
                                  fullWidth
                                  variant="outlined"
                                  displayEmpty={mode !== LifestyleMode.VIEW}
                                  disabled={readonly}
                                  open={isSelectOpen(rowIndex, header.id)}
                                  onOpen={() =>
                                    setSelectOpen(rowIndex, header.id, true)
                                  }
                                  onClose={() =>
                                    setSelectOpen(rowIndex, header.id, false)
                                  }
                                  IconComponent={
                                    mode === LifestyleMode.VIEW
                                      ? () => null
                                      : () => (
                                          <IoIosArrowDown
                                            className="h-4 w-auto text-[#637D92] mr-2 cursor-pointer"
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              if (!readonly) {
                                                toggleSelectOpen(
                                                  rowIndex,
                                                  header.id
                                                );
                                              }
                                            }}
                                          />
                                        )
                                  }
                                  sx={{
                                    '&&& .MuiInputBase-root': {
                                      backgroundColor: 'transparent !important',
                                      color:
                                        mode === LifestyleMode.VIEW
                                          ? '#000000 !important'
                                          : 'inherit',
                                      border: 'none !important',
                                      outline: 'none !important',
                                      borderColor: 'transparent !important',
                                      boxShadow: 'none !important',
                                      '&:before': {
                                        border: 'none !important',
                                        borderBottom: 'none !important',
                                        display: 'none !important',
                                      },
                                      '&:after': {
                                        border: 'none !important',
                                        borderBottom: 'none !important',
                                        display: 'none !important',
                                      },
                                      '&:hover': {
                                        backgroundColor:
                                          'transparent !important',
                                        border: 'none !important',
                                        borderColor: 'transparent !important',
                                        '&:before': {
                                          border: 'none !important',
                                          borderBottom: 'none !important',
                                          display: 'none !important',
                                        },
                                      },
                                      '&.Mui-focused': {
                                        backgroundColor:
                                          'transparent !important',
                                        border: 'none !important',
                                        borderColor: 'transparent !important',
                                        boxShadow: 'none !important',
                                        '&:after': {
                                          border: 'none !important',
                                          borderBottom: 'none !important',
                                          display: 'none !important',
                                        },
                                      },
                                      '&.Mui-disabled': {
                                        backgroundColor:
                                          'transparent !important',
                                        color:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                        border: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                    },
                                    '&&& .MuiOutlinedInput-root': {
                                      backgroundColor: 'transparent !important',
                                      color:
                                        mode === LifestyleMode.VIEW
                                          ? '#000000 !important'
                                          : 'inherit',
                                      border: 'none !important',
                                      outline: 'none !important',
                                      borderColor: 'transparent !important',
                                      '&:hover': {
                                        backgroundColor:
                                          'transparent !important',
                                        border: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                      '&.Mui-focused': {
                                        backgroundColor:
                                          'transparent !important',
                                        border: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                      '&.Mui-disabled': {
                                        backgroundColor:
                                          'transparent !important',
                                        color:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                        border: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                      '& fieldset': {
                                        border: 'none !important',
                                        outline: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                      '&:hover fieldset': {
                                        border: 'none !important',
                                        outline: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                      '&.Mui-focused fieldset': {
                                        border: 'none !important',
                                        outline: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                      '& .MuiOutlinedInput-notchedOutline': {
                                        border: 'none !important',
                                        outline: 'none !important',
                                        display: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                    },
                                    '& .MuiInput-underline': {
                                      '&:before': {
                                        borderBottom: 'none !important',
                                        display: 'none !important',
                                      },
                                      '&:after': {
                                        borderBottom: 'none !important',
                                        display: 'none !important',
                                      },
                                      '&:hover:not(.Mui-disabled):before': {
                                        borderBottom: 'none !important',
                                        display: 'none !important',
                                      },
                                    },
                                    '& .MuiSelect-select': {
                                      backgroundColor: 'transparent !important',
                                      color:
                                        mode === LifestyleMode.VIEW
                                          ? '#000000 !important'
                                          : 'inherit',
                                      '&:focus': {
                                        backgroundColor:
                                          'transparent !important',
                                        color:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                      },
                                      '&.Mui-disabled': {
                                        backgroundColor:
                                          'transparent !important',
                                        color:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                        WebkitTextFillColor:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                      },
                                    },
                                  }}
                                  renderValue={(selected) => {
                                    if (
                                      !selected &&
                                      mode !== LifestyleMode.VIEW
                                    ) {
                                      return (
                                        <span style={{ color: '#9ca3af' }}>
                                          Select
                                        </span>
                                      );
                                    }
                                    return selected || '';
                                  }}
                                >
                                  {selectOptions.map((option) => (
                                    <MenuItem key={option} value={option}>
                                      {option}
                                    </MenuItem>
                                  ))}
                                </Select>
                              );
                            } else if (header.type === 'number') {
                              const isEditable = isFieldEditable(
                                rowIndex,
                                header.id
                              );
                              // In CREATE mode, duration fields are always editable
                              // In EDIT mode, duration fields are editable based on toggle state
                              const fieldDisabled =
                                readonly ||
                                (mode === LifestyleMode.EDIT && !isEditable);

                              return (
                                <TextField
                                  {...controllerField}
                                  value={controllerField.value || ''}
                                  type="text"
                                  size="small"
                                  variant="outlined"
                                  disabled={fieldDisabled}
                                  onChange={(e) => {
                                    const value = e.target.value;

                                    if (
                                      value === '' ||
                                      /^\d*\.?\d*$/.test(value)
                                    ) {
                                      controllerField.onChange(value);
                                    }
                                  }}
                                  onKeyPress={(e) => {
                                    if (
                                      !/[\d.]/.test(e.key) &&
                                      e.key !== 'Backspace' &&
                                      e.key !== 'Delete' &&
                                      e.key !== 'Tab' &&
                                      e.key !== 'Enter'
                                    ) {
                                      e.preventDefault();
                                    }
                                  }}
                                  InputProps={{
                                    endAdornment: !readonly &&
                                      mode === LifestyleMode.EDIT && (
                                        <IconButton
                                          size="small"
                                          onClick={() =>
                                            toggleFieldEdit(rowIndex, header.id)
                                          }
                                          sx={{
                                            padding: '2px',
                                            marginRight: '-16px',
                                            color: isEditable
                                              ? '#2563eb'
                                              : '#6b7280',
                                          }}
                                        >
                                          <MdEdit className="h-4 w-4" />
                                        </IconButton>
                                      ),
                                    sx: {
                                      paddingRight:
                                        mode === LifestyleMode.EDIT
                                          ? '4px'
                                          : '16px',
                                    },
                                  }}
                                  sx={{
                                    width: '80px',
                                    minWidth: '80px',
                                    maxWidth: '80px',
                                    '&&& .MuiInputBase-root': {
                                      backgroundColor: 'transparent !important',
                                      border: 'none !important',
                                      outline: 'none !important',
                                      borderColor: 'transparent !important',
                                      boxShadow: 'none !important',
                                      color:
                                        mode === LifestyleMode.VIEW
                                          ? '#000000 !important'
                                          : 'inherit',
                                      '&:before': {
                                        border: 'none !important',
                                        borderBottom: 'none !important',
                                        display: 'none !important',
                                      },
                                      '&:after': {
                                        border: 'none !important',
                                        borderBottom: 'none !important',
                                        display: 'none !important',
                                      },
                                      '&:hover': {
                                        backgroundColor:
                                          'transparent !important',
                                        border: 'none !important',
                                        borderColor: 'transparent !important',
                                        '&:before': {
                                          border: 'none !important',
                                          borderBottom: 'none !important',
                                          display: 'none !important',
                                        },
                                      },
                                      '&.Mui-focused': {
                                        backgroundColor:
                                          'transparent !important',
                                        border: 'none !important',
                                        borderColor: 'transparent !important',
                                        boxShadow: 'none !important',
                                        '&:after': {
                                          border: 'none !important',
                                          borderBottom: 'none !important',
                                          display: 'none !important',
                                        },
                                      },
                                      '&.Mui-disabled': {
                                        backgroundColor:
                                          'transparent !important',
                                        color:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                        border: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                    },
                                    '&&& .MuiOutlinedInput-root': {
                                      backgroundColor: 'transparent !important',
                                      border: 'none !important',
                                      outline: 'none !important',
                                      borderColor: 'transparent !important',
                                      color:
                                        mode === LifestyleMode.VIEW
                                          ? '#000000 !important'
                                          : 'inherit',
                                      '&:hover': {
                                        backgroundColor:
                                          'transparent !important',
                                        border: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                      '&.Mui-focused': {
                                        backgroundColor:
                                          'transparent !important',
                                        border: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                      '&.Mui-disabled': {
                                        backgroundColor:
                                          'transparent !important',
                                        color:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                        border: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                      '& fieldset': {
                                        border: 'none !important',
                                        outline: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                      '&:hover fieldset': {
                                        border: 'none !important',
                                        outline: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                      '&.Mui-focused fieldset': {
                                        border: 'none !important',
                                        outline: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                      '& .MuiOutlinedInput-notchedOutline': {
                                        border: 'none !important',
                                        outline: 'none !important',
                                        display: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                    },
                                    '& .MuiInput-underline': {
                                      '&:before': {
                                        borderBottom: 'none !important',
                                        display: 'none !important',
                                      },
                                      '&:after': {
                                        borderBottom: 'none !important',
                                        display: 'none !important',
                                      },
                                      '&:hover:not(.Mui-disabled):before': {
                                        borderBottom: 'none !important',
                                        display: 'none !important',
                                      },
                                    },
                                    '& .MuiInputBase-input': {
                                      backgroundColor: 'transparent !important',
                                      color:
                                        mode === LifestyleMode.VIEW
                                          ? '#000000 !important'
                                          : 'inherit',
                                      '&:-webkit-autofill': {
                                        WebkitBoxShadow:
                                          '0 0 0 1000px transparent inset !important',
                                        backgroundColor:
                                          'transparent !important',
                                        color:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                      },
                                      '&:-webkit-autofill:hover': {
                                        WebkitBoxShadow:
                                          '0 0 0 1000px transparent inset !important',
                                        backgroundColor:
                                          'transparent !important',
                                        color:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                      },
                                      '&:-webkit-autofill:focus': {
                                        WebkitBoxShadow:
                                          '0 0 0 1000px transparent inset !important',
                                        backgroundColor:
                                          'transparent !important',
                                        color:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                      },
                                      '&:focus': {
                                        backgroundColor:
                                          'transparent !important',
                                        color:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                      },
                                      '&.Mui-disabled': {
                                        color:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                        backgroundColor:
                                          'transparent !important',
                                        WebkitTextFillColor:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                      },
                                    },
                                  }}
                                />
                              );
                            } else {
                              return (
                                <TextField
                                  {...controllerField}
                                  value={controllerField.value || ''}
                                  size="small"
                                  fullWidth
                                  variant="outlined"
                                  disabled={readonly}
                                  sx={{
                                    '&&& .MuiInputBase-root': {
                                      backgroundColor: 'transparent !important',
                                      color:
                                        mode === LifestyleMode.VIEW
                                          ? '#000000 !important'
                                          : 'inherit',
                                      border: 'none !important',
                                      outline: 'none !important',
                                      borderColor: 'transparent !important',
                                      boxShadow: 'none !important',
                                      '&:before': {
                                        border: 'none !important',
                                        borderBottom: 'none !important',
                                        display: 'none !important',
                                      },
                                      '&:after': {
                                        border: 'none !important',
                                        borderBottom: 'none !important',
                                        display: 'none !important',
                                      },
                                      '&:hover': {
                                        backgroundColor:
                                          'transparent !important',
                                        border: 'none !important',
                                        borderColor: 'transparent !important',
                                        '&:before': {
                                          border: 'none !important',
                                          borderBottom: 'none !important',
                                          display: 'none !important',
                                        },
                                      },
                                      '&.Mui-focused': {
                                        backgroundColor:
                                          'transparent !important',
                                        border: 'none !important',
                                        borderColor: 'transparent !important',
                                        boxShadow: 'none !important',
                                        '&:after': {
                                          border: 'none !important',
                                          borderBottom: 'none !important',
                                          display: 'none !important',
                                        },
                                      },
                                      '&.Mui-disabled': {
                                        backgroundColor:
                                          'transparent !important',
                                        color:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                        border: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                    },
                                    '&&& .MuiOutlinedInput-root': {
                                      backgroundColor: 'transparent !important',
                                      color:
                                        mode === LifestyleMode.VIEW
                                          ? '#000000 !important'
                                          : 'inherit',
                                      border: 'none !important',
                                      outline: 'none !important',
                                      borderColor: 'transparent !important',
                                      '&:hover': {
                                        backgroundColor:
                                          'transparent !important',
                                        border: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                      '&.Mui-focused': {
                                        backgroundColor:
                                          'transparent !important',
                                        border: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                      '&.Mui-disabled': {
                                        backgroundColor:
                                          'transparent !important',
                                        color:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                        border: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                      '& fieldset': {
                                        border: 'none !important',
                                        outline: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                      '&:hover fieldset': {
                                        border: 'none !important',
                                        outline: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                      '&.Mui-focused fieldset': {
                                        border: 'none !important',
                                        outline: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                      '& .MuiOutlinedInput-notchedOutline': {
                                        border: 'none !important',
                                        outline: 'none !important',
                                        display: 'none !important',
                                        borderColor: 'transparent !important',
                                      },
                                    },
                                    '& .MuiInputBase-input': {
                                      color:
                                        mode === LifestyleMode.VIEW
                                          ? '#000000 !important'
                                          : 'inherit',
                                      backgroundColor: 'transparent !important',
                                      '&:focus': {
                                        backgroundColor:
                                          'transparent !important',
                                        color:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                      },
                                      '&.Mui-disabled': {
                                        color:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                        backgroundColor:
                                          'transparent !important',
                                        WebkitTextFillColor:
                                          mode === LifestyleMode.VIEW
                                            ? '#000000 !important'
                                            : 'inherit',
                                      },
                                    },
                                  }}
                                />
                              );
                            }
                          }}
                        />
                      </td>
                    );
                  })}
                  {!readonly && (
                    <td className="px-4 py-2 text-center">
                      <IconButton
                        onClick={() => handleDeleteClick(rowIndex)}
                        size="small"
                        sx={{
                          color: '#ef4444',
                          border: '1px solid #ef4444',
                          borderRadius: '50%',
                          width: '32px',
                          height: '32px',
                          '&:hover': {
                            backgroundColor: '#fef2f2',
                            color: '#dc2626',
                            borderColor: '#dc2626',
                          },
                        }}
                      >
                        <MdClose size={18} />
                      </IconButton>
                    </td>
                  )}
                </tr>
              ))}
              {fields.length === 0 && (
                <tr>
                  <td
                    colSpan={tableField.headers.length + (!readonly ? 1 : 0)}
                    className="text-center py-4 text-gray-500 border-b border-gray-300"
                  >
                    No entry yet
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        <DeleteModal
          open={deleteModalOpen}
          onClose={handleCancelDelete}
          onDelete={handleConfirmDelete}
          confirmationMessage="Are you sure you want to delete this entry?"
          classes={{ container: '!w-[400px]' }}
          bodyContent={
            getActivityType() || getActivity() ? (
              <div className="mt-2 text-center">
                <div className="text-xl">
                  {getActivity() && (
                    <span className="font-medium" style={{ color: '#0496E1' }}>
                      {getActivity()}
                    </span>
                  )}
                  {getActivity() && getActivityType() && (
                    <>
                      <span className="text-black"> in </span>
                      <span className="text-black font-bold">
                        {getActivityType()}
                      </span>
                    </>
                  )}
                  {!getActivity() && getActivityType() && (
                    <span className="text-black font-bold">
                      {getActivityType()}
                    </span>
                  )}
                </div>
              </div>
            ) : null
          }
        />
      </div>
    </>
  );
};
