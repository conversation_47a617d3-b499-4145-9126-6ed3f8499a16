import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import type { MutableRefObject } from 'react';

import { FormProvider, useForm } from 'react-hook-form';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { lifestyleStore } from '@/store/emr/lifestyle/lifestyle-store';
import { foodFrequencyQuestionnaireStore } from '@/store/emr/lifestyle/nutrition/practice/food-frequency-questionnaire-store';

import {
  LifestyleMode,
  LifestyleRecordStatus,
} from '@/constants/emr/lifestyle';

import ModalWrapper from '@/views/emr/lifestyle/shared/ModalWrapper';
import { BaseLifestyleModalProps } from '@/views/emr/lifestyle/shared/types';

import { QuestionnaireResponse } from '@/types/emr/lifestyle/questionnaire';

import FoodFrequencyQuestionnaireForm from './FoodFrequencyQuestionnaireForm';

const FoodFrequencyQuestionnaireModal: React.FC<
  BaseLifestyleModalProps & {
    onSaveRef?: MutableRefObject<(() => void) | null>;
  }
> = ({
  patientData,
  mode = LifestyleMode.CREATE,
  onAfterSubmit,
  hideSaveButton = false,
  onSaveRef,
}) => {
  const {
    getLifestyleQuestions,
    questions,
    questionLoading,
    updating,
    createLifestyleData,
    updateLifestyleData,
  } = foodFrequencyQuestionnaireStore();
  const { setModalOpen } = lifestyleStore();
  const profile = useDoctorStore((state) => state.doctorProfile);

  const [currentMode, setCurrentMode] = useState(mode);

  const methods = useForm<QuestionnaireResponse>({
    defaultValues: patientData ?? questions,
    mode: 'onChange',
  });

  const { handleSubmit } = methods;

  const formFields = useMemo(() => {
    if (!questions?.questions?.length) return [];

    return questions.questions;
  }, [questions]);

  const onSubmit = useCallback(
    async (data: QuestionnaireResponse) => {
      try {
        if (data?.id) {
          const updateData = {
            ...data,
            doctor: {
              id: profile?.id ?? '',
              name: profile?.general?.fullName,
              designation: profile?.general?.designation,
              department: profile?.general?.department,
            },

            questions:
              data.questions || patientData?.questions || questions.questions,
          };

          if (!updateData.questions || !Array.isArray(updateData.questions)) {
            console.error(
              ' Questions data is missing or invalid:',
              updateData.questions
            );
            throw new Error('Questions data is required for update');
          }

          await updateLifestyleData(updateData);
        } else {
          const createData = {
            ...data,
            doctor: {
              id: profile?.id ?? '',
              name: profile?.general?.fullName,
              designation: profile?.general?.designation,
              department: profile?.general?.department,
            },
          };

          await createLifestyleData(createData);
        }
        setModalOpen(false);
        onAfterSubmit?.();
      } catch (error) {
        console.error(' Error submitting food frequency questionnaire:', error);
      }
    },
    [
      setModalOpen,
      profile?.id,
      profile?.general?.fullName,
      profile?.general?.designation,
      profile?.general?.department,
      patientData?.questions,
      questions.questions,
      updateLifestyleData,
      createLifestyleData,
      onAfterSubmit,
    ]
  );

  const handleSaveClick = useCallback(() => {
    handleSubmit(onSubmit)();
  }, [handleSubmit, onSubmit]);

  useEffect(() => {
    if (onSaveRef) {
      onSaveRef.current = handleSaveClick;
    }
  }, [onSaveRef, handleSaveClick]);

  useEffect(() => {
    getLifestyleQuestions();
  }, [getLifestyleQuestions]);

  useEffect(() => {
    if (patientData) {
      methods.reset(patientData);
    } else if (questions) {
      methods.reset(questions);
    }
  }, [patientData, questions, methods]);

  return (
    <FormProvider {...methods}>
      <ModalWrapper
        loading={questionLoading}
        onSubmit={handleSubmit(onSubmit)}
        updating={updating}
        mode={currentMode}
        onEdit={() => setCurrentMode(LifestyleMode.EDIT)}
        finalized={patientData?.status === LifestyleRecordStatus.FINALIZED}
        hideSaveButton={hideSaveButton}
      >
        <FoodFrequencyQuestionnaireForm
          formFields={formFields}
          readonly={currentMode === LifestyleMode.VIEW}
        />
      </ModalWrapper>
    </FormProvider>
  );
};

export default memo(FoodFrequencyQuestionnaireModal);
