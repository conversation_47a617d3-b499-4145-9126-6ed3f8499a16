# Consultation Ambient Listening - Technical Summary

## Overview for Leadership

The **Consultation Ambient Listening** feature is an AI-powered solution that automatically records, transcribes, and processes doctor-patient conversations during consultations. This technology significantly reduces manual documentation time while improving the accuracy and completeness of medical records.

## Technical Implementation

### Core Technologies Used

**Frontend Technologies:**
- **Microsoft Azure Speech SDK** (`microsoft-cognitiveservices-speech-sdk`) - Handles real-time speech-to-text conversion
- **ConversationTranscriber API** - Azure's advanced transcription service for multi-speaker conversations
- **React TypeScript** - Modern UI framework for responsive user interface
- **Zustand State Management** - Efficient state handling for recording states and transcript data
- **Canvas API** - Real-time sine wave visualization during recording
- **WebRTC Audio APIs** - Browser microphone access and audio capture

**Backend APIs:**
- **Azure Speech Services** - Cloud-based speech recognition and transcription
- **OpenAI GPT API** - AI-powered conversation analysis and medical information extraction
- **Custom REST API** (`/summary` endpoint) - Processes transcripts and returns structured medical summaries
- **Authentication API** (`getSpeechToken()`) - Secure token management for Azure services

### Workflow Process

The system follows a streamlined 5-step process: **Language Selection** → **Speech Engine Initialization** → **Real-time Recording & Transcription** → **AI Processing** → **Medical Summary Generation**. During recording, the Azure Speech SDK captures audio through the browser's microphone, converts speech to text in real-time, and displays live transcription with visual feedback. Once recording ends, the transcript is sent to our backend API which uses OpenAI's GPT models to identify speakers (doctor vs patient) and extract structured medical information including presenting complaints, medical history, examination findings, and treatment plans.

### Key Features

**Multi-language Support:** Supports 7 languages (English, Malayalam, Tamil, Kannada, Telugu, Bengali, Hindi) using Azure's regional speech models. **Real-time Processing:** Live transcription with auto-scrolling display and visual recording indicators. **AI-Powered Analysis:** Automatic speaker identification and medical information extraction using advanced NLP models. **Secure Architecture:** No audio storage on servers, encrypted data transmission, and secure token-based authentication with Azure services.

### Business Impact

This solution reduces consultation documentation time by approximately 60-70%, allows doctors to maintain better eye contact and engagement with patients, ensures comprehensive and accurate medical records, and provides structured data that integrates seamlessly with our EMR system. The technology leverages enterprise-grade cloud services (Microsoft Azure) and industry-leading AI models (OpenAI) to deliver reliable, scalable, and secure ambient listening capabilities for modern healthcare practices.
