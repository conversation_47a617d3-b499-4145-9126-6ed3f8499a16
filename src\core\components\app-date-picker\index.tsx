import * as React from 'react';

import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import {
  DatePicker as MuiDatePicker,
  DatePickerProps as MuiDatePickerProps,
} from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import dayjs, { Dayjs } from 'dayjs';

import { DateFormats } from '@/utils/dateUtils/dateFormats';

import InputLabel from '../input-label';

import { StyledDatePicker } from './styled-components';

export type AppDatePickerProps = Omit<
  MuiDatePickerProps<Dayjs>,
  'value' | 'onChange'
> & {
  label?: string;
  value?: string | null;
  onChange?: (value: string | null) => void;
  inputClassName?: string;
  disableManualInput?: boolean;
  error?: string;
  required?: boolean;
  fieldHeight?: string | number; // New prop for field height
  // Edit icon/option additions
  initiallyReadonly?: boolean;
  editIconClassName?: string;
  onEditToggle?: (isEditing: boolean) => void;
  isReadOnly?: boolean;
};

const AppDatePicker: React.FC<AppDatePickerProps> = ({
  label,
  value,
  onChange,
  inputClassName,
  disableManualInput,
  error,
  required,
  fieldHeight,
  initiallyReadonly = false,
  editIconClassName = '',
  onEditToggle,
  isReadOnly: datePickerReadOnly = false,
  format = DateFormats.DATE_DD_MM_YYYY_SLASH,
  ...rest
}) => {
  const [isEditing, setIsEditing] = React.useState(
    !initiallyReadonly || !value
  );

  // Default height if not provided
  const heightStyle = fieldHeight
    ? {
        height:
          typeof fieldHeight === 'number' ? `${fieldHeight}px` : fieldHeight,
      }
    : {};

  const shouldShowEditIcon = initiallyReadonly && value && !isEditing;
  // const isReadOnly = initiallyReadonly && value && !isEditing;

  const handleEditToggle = () => {
    const newEditingState = !isEditing;
    setIsEditing(newEditingState);
    onEditToggle?.(newEditingState);
  };

  return (
    <StyledDatePicker
      className={`relative flex flex-col text-[#001926] w-full ${inputClassName ?? ''}`}
    >
      {label && (
        <InputLabel
          label={label}
          required={required}
          className="mb-0 md:mb-0 text-sm md:text-sm"
        />
      )}
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <MuiDatePicker
          {...rest}
          value={value ? dayjs(value) : null}
          format={format}
          onChange={(date) => {
            if (onChange) {
              onChange(
                date && dayjs(date).isValid() ? dayjs(date).toISOString() : null
              );
            }
          }}
          slotProps={{
            textField: {
              size: 'small',
              variant: 'outlined',
              error: !!error,
              helperText: error,
              inputProps: {
                readOnly: Boolean(datePickerReadOnly || disableManualInput),
              },
              sx: {
                backgroundColor: rest.disabled ? '#fafafc' : undefined,
                '& .MuiInputBase-root': {
                  ...heightStyle,
                  '& .MuiInputBase-input': {
                    py: '8.5px', // Default padding, can be overridden by sx prop
                    ...(fieldHeight && { py: 0 }), // Remove vertical padding if height is set
                  },
                },
                ...rest.sx,
              },
              ...rest.slotProps?.textField,
            },
            ...(rest.slotProps || {}),
          }}
        />
      </LocalizationProvider>
    </StyledDatePicker>
  );
};

export default AppDatePicker;
