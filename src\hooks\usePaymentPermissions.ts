import { useEffect } from 'react';

import { useCustomiseEmrStore } from '@/store/emr/doctor-profile/customise-emr';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useUserStore } from '@/store/userStore';

import { PERMISSION_KEYS } from '@/constants/permission-keys';

import { hasPermission } from '@/core/lib/auth/permissions';

export interface PaymentPermissions {
  hasPaymentCreate: boolean;
  hasPatientRegistrationPayment: boolean;
  hasAppointmentBookingPayment: boolean;
  hasPrescriptionPayment: boolean;
  hasLabTestPayment: boolean;
}

export interface PaymentSettings {
  patientRegistrationEnabled: boolean;
  appointmentBookingEnabled: boolean;
  prescriptionEnabled: boolean;
  labTestEnabled: boolean;
}

/**
 * Hook to check user's payment permissions and settings
 * @returns Object containing permission checks and payment settings
 */
export const usePaymentPermissions = (): PaymentPermissions & {
  paymentSettings: PaymentSettings;
  isPaymentEnabled: (feature: keyof PaymentSettings) => boolean;
} => {
  const { permissions = [] } = useUserStore();
  const { customiseEmrData, fetchCustomiseEmr } = useCustomiseEmrStore();
  const { doctorProfile } = useDoctorStore();

  // Check base permissions
  const paymentPermissions: PaymentPermissions = {
    hasPaymentCreate: hasPermission(
      permissions,
      PERMISSION_KEYS.PAYMENT_CREATE
    ),
    hasPatientRegistrationPayment: hasPermission(
      permissions,
      PERMISSION_KEYS.MRD_PAYMENT_PATIENT_REGISTRATION
    ),
    hasAppointmentBookingPayment: hasPermission(
      permissions,
      PERMISSION_KEYS.EMR_PAYMENT_APPOINTMENT_BOOKING
    ),
    hasPrescriptionPayment: hasPermission(
      permissions,
      PERMISSION_KEYS.EMR_PAYMENT_PRESCRIPTION
    ),
    hasLabTestPayment: hasPermission(
      permissions,
      PERMISSION_KEYS.EMR_PAYMENT_LAB_TEST
    ),
  };

  const hasDoctorProfilePermissions =
    hasPermission(permissions, PERMISSION_KEYS.EMR_DOCTOR_PROFILE_VIEW) &&
    hasPermission(permissions, PERMISSION_KEYS.EMR_DOCTOR_PROFILE_EDIT);

  useEffect(() => {
    if (hasDoctorProfilePermissions && doctorProfile?.id) {
      fetchCustomiseEmr(doctorProfile.id);
    }
  }, [hasDoctorProfilePermissions, doctorProfile?.id, fetchCustomiseEmr]);

  const latestCustomization =
    customiseEmrData && customiseEmrData.length > 0
      ? customiseEmrData[customiseEmrData.length - 1]
      : null;

  const paymentSettings: PaymentSettings = {
    patientRegistrationEnabled:
      hasDoctorProfilePermissions && latestCustomization
        ? latestCustomization?.paymentPatientRegistration === true
        : paymentPermissions.hasPatientRegistrationPayment,
    appointmentBookingEnabled:
      hasDoctorProfilePermissions && latestCustomization
        ? latestCustomization?.paymentAppointmentBooking === true
        : paymentPermissions.hasAppointmentBookingPayment,
    prescriptionEnabled:
      hasDoctorProfilePermissions && latestCustomization
        ? latestCustomization?.paymentPrescription === true
        : paymentPermissions.hasPrescriptionPayment,
    labTestEnabled:
      hasDoctorProfilePermissions && latestCustomization
        ? latestCustomization?.paymentLabTest === true
        : paymentPermissions.hasLabTestPayment,
  };

  const isPaymentEnabled = (feature: keyof PaymentSettings): boolean => {
    if (!paymentPermissions.hasPaymentCreate) return false;

    switch (feature) {
      case 'patientRegistrationEnabled':
        return (
          paymentPermissions.hasPatientRegistrationPayment &&
          paymentSettings.patientRegistrationEnabled
        );
      case 'appointmentBookingEnabled':
        return (
          paymentPermissions.hasAppointmentBookingPayment &&
          paymentSettings.appointmentBookingEnabled
        );
      case 'prescriptionEnabled':
        return (
          paymentPermissions.hasPrescriptionPayment &&
          paymentSettings.prescriptionEnabled
        );
      case 'labTestEnabled':
        return (
          paymentPermissions.hasLabTestPayment && paymentSettings.labTestEnabled
        );
      default:
        return false;
    }
  };

  return {
    ...paymentPermissions,
    paymentSettings,
    isPaymentEnabled,
  };
};

export default usePaymentPermissions;
