import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import type { MutableRefObject } from 'react';

import { FormProvider, useForm } from 'react-hook-form';

import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { lifestyleStore } from '@/store/emr/lifestyle/lifestyle-store';
import { nutritionAttitudeStore } from '@/store/emr/lifestyle/nutrition/attitude/attitude-store';

import {
  LifestyleMode,
  LifestyleRecordStatus,
} from '@/constants/emr/lifestyle';

import ModalWrapper from '@/views/emr/lifestyle/shared/ModalWrapper';
import { BaseLifestyleModalProps } from '@/views/emr/lifestyle/shared/types';

import { QuestionnaireResponse } from '@/types/emr/lifestyle/questionnaire';

import AttitudeForm from './AttitudeForm';

const AttitudeModal: React.FC<
  BaseLifestyleModalProps & {
    onSaveRef?: MutableRefObject<(() => void) | null>;
  }
> = ({
  patientData,
  mode = LifestyleMode.CREATE,
  onAfterSubmit,
  hideSaveButton = false,
  onSaveRef,
}) => {
  const {
    getLifestyleQuestions,
    questions,
    questionLoading,
    updating,
    createLifestyleData,
    updateLifestyleData,
  } = nutritionAttitudeStore();
  const { setModalOpen } = lifestyleStore();
  const profile = useDoctorStore((state) => state.doctorProfile);

  const [currentMode, setCurrentMode] = useState(mode);

  const methods = useForm<QuestionnaireResponse>({
    defaultValues: patientData ?? questions,
    mode: 'onChange',
  });

  const { handleSubmit, reset } = methods;

  const formFields = useMemo(() => {
    if (patientData?.questions?.length) return patientData.questions;
    if (!questions?.questions?.length) return [];
    return questions.questions;
  }, [questions, patientData]);

  const onSubmit = useCallback(
    async (data: QuestionnaireResponse) => {
      try {
        if (data?.id) {
          const updateData = {
            ...data,
            doctor: {
              id: profile?.id ?? '',
              name: profile?.general?.fullName,
              designation: profile?.general?.designation,
              department: profile?.general?.department,
            },
          };
          await updateLifestyleData(updateData);
        } else {
          const createData = {
            ...data,
            doctor: {
              id: profile?.id ?? '',
              name: profile?.general?.fullName,
              designation: profile?.general?.designation,
              department: profile?.general?.department,
            },
          };
          await createLifestyleData(createData);
        }
        setModalOpen(false);
        onAfterSubmit?.();
      } catch (error) {
        console.error('Error submitting nutrition attitude:', error);
      }
    },
    [
      profile,
      updateLifestyleData,
      createLifestyleData,
      setModalOpen,
      onAfterSubmit,
    ]
  );

  const handleSaveClick = useCallback(() => {
    handleSubmit(onSubmit)();
  }, [handleSubmit, onSubmit]);

  useEffect(() => {
    if (onSaveRef) {
      onSaveRef.current = handleSaveClick;
    }
  }, [onSaveRef, handleSaveClick]);
  useEffect(() => {
    if (
      !patientData &&
      (!questions?.questions?.length || questions?.questions?.length === 0)
    ) {
      getLifestyleQuestions();
    }
  }, [getLifestyleQuestions, questions, patientData]);

  useEffect(() => {
    if (patientData) {
      reset(patientData);
    } else if (questions?.questions?.length) {
      reset(questions);
    }
  }, [patientData, questions, reset]);

  useEffect(() => {
    setCurrentMode(mode);
  }, [mode]);

  return (
    <FormProvider {...methods}>
      <ModalWrapper
        loading={questionLoading}
        onSubmit={handleSubmit(onSubmit)}
        updating={updating}
        mode={currentMode}
        onEdit={() => setCurrentMode(LifestyleMode.EDIT)}
        finalized={patientData?.status === LifestyleRecordStatus.FINALIZED}
        hideSaveButton={hideSaveButton}
      >
        <AttitudeForm
          formFields={formFields}
          readonly={currentMode === LifestyleMode.VIEW}
          showHeading={true}
        />
      </ModalWrapper>
    </FormProvider>
  );
};

export default memo(AttitudeModal);
