import { formatICD, formatSnomedCT } from '@/utils/medical-codes';

import { api, arcaAxios } from '@/core/lib/interceptor';
import { SnomedApiResponse, ICDApiResponse } from '@/types/medical-codes';

export type SnomedParams = {
  term: string;
};

export type ICDParams = {
  q: string;
};

export const fetchSnomedCT = async (
  params: SnomedParams
): Promise<SnomedApiResponse> => {
  const { data } = await arcaAxios.get<SnomedApiResponse>(`/snomed-proxy`, {
    params,
  });
  return data;
};

export const fetchICDCode = async (
  params: ICDParams
): Promise<ICDApiResponse> => {
  // const { data } = await arcaAxios.get<ICDApiResponse>(`/icd-proxy`, {
  const { data } = await api.get<ICDApiResponse>(
    `/consultation/v0.1/icd-both-proxy`,
    {
      params,
    }
  );
  return data;
};

export const searchMedicalCodes = async (query: string) => {
  try {
    let snomedRes: SnomedApiResponse = {} as SnomedApiResponse;
    let icdRes: ICDApiResponse = {} as ICDApiResponse;

    try {
      snomedRes = await fetchSnomedCT({ term: query });
    } catch (error) {
      console.error(error);
    }

    try {
      icdRes = await fetchICDCode({ q: query });
    } catch (error) {
      console.error(error);
    }

    const formattedSnomedRes = formatSnomedCT(snomedRes);
    const formattedIcdRes = formatICD(icdRes);

    return [...formattedSnomedRes, ...formattedIcdRes];
  } catch (error) {
    console.error(error);
    return [];
  }
};
