'use client';

import { useEffect, useState } from 'react';

import { useForm } from 'react-hook-form';

import { maxBy } from 'lodash';
import * as speechsdk from 'microsoft-cognitiveservices-speech-sdk';
import { BiSave, BiX } from 'react-icons/bi';
import { toast } from 'sonner';

import {
  EmrTypes,
  useCustomiseEmrStore,
} from '@/store/emr/doctor-profile/customise-emr';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useAmbientStore } from '@/store/emr/lifestyle/ambient-listening/ambient-store';

import {
  getSpeechToken,
  SummarizeConversationRes,
  summarizeConversation,
  summarizeAmbientListening,
} from '@/query/speech';

import { CurrentModal, currentModal } from '@/constants/ambient-listening';

import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import SummaryForm from '@/emr/components/consultation/summary-form';
import Transcript from '@/emr/components/transcript';

import './styles.scss';

import AppModal from '@/core/components/app-modal';
import PrimaryButton from '@/core/components/primary-button';

import Loading from '../common/loading';
import RecordConsultation from '../modals/RecordConsultation';

const RecordingLanguage = {
  English: 'en-IN',
  Malayalam: 'ml-IN',
  Tamil: 'ta-IN',
  Kannada: 'kn-IN',
  Telugu: 'te-IN',
  Bengali: 'bn-IN',
  Hindi: 'hi-IN',
} as const;

type LanguageType = (typeof RecordingLanguage)[keyof typeof RecordingLanguage];

const {
  INITIAL,
  RECORD_CONSULTATION,
  LOADING,
  SHOW_SUMMARY,
  LANGUAGE_SELECTION,
} = currentModal;

export interface AddRecordProps {
  onSave?: (data: SummarizeConversationRes['summary']) => void;
  disabled?: boolean;
  isLoading?: boolean;
  summaryFormTitle?: string;
  summaryFormSubtitle?: string;
  modalTitle?: string;
  customSummaryForm?: React.ReactNode;
  isAmbientRecord?: boolean;
  source?: string;
}

const AddRecord = ({
  onSave = () => {},
  disabled = false,
  isLoading = false,
  summaryFormTitle = 'Consultation',
  modalTitle = '',
  customSummaryForm,
  isAmbientRecord = false,
  source = '',
}: AddRecordProps) => {
  const { doctorProfile } = useDoctorStore();

  const { customiseEmrData, fetchCustomiseEmr } = useCustomiseEmrStore();

  const customEmrData = maxBy<EmrTypes>(
    customiseEmrData,
    (item) => new Date(item.created_on as string)
  );

  const emrLanguage = customEmrData?.preferred_language_for_ambient_listening;

  const doctorLanguage = Object.entries(RecordingLanguage).find(
    ([key]) => key === emrLanguage
  )?.[1];

  const [engine, setEngine] =
    useState<speechsdk.ConversationTranscriber | null>(null);
  const [response, setResponse] = useState<SummarizeConversationRes | null>(
    null
  );
  const [currentMode, setCurrentMode] = useState<CurrentModal>(INITIAL);
  const [language, setLanguage] = useState<LanguageType>(
    RecordingLanguage.English
  );
  const [isModalOpen, setIsModalOpen] = useState(false);

  const form = useForm<SummarizeConversationRes['summary']>();

  const startFlow = () => {
    setCurrentMode(LANGUAGE_SELECTION);
    setIsModalOpen(true);
  };

  const handleClose = () => {
    if (isLoading) return; // Prevent closing while saving
    setCurrentMode(INITIAL);
    setLanguage(doctorLanguage || RecordingLanguage.English);
    setIsModalOpen(false);
  };

  const initializeSpeechEngine = async (language: string) => {
    const speechToken = await getSpeechToken();
    const speechConfig = speechsdk.SpeechConfig.fromAuthorizationToken(
      speechToken,
      'eastus'
    );
    speechConfig.speechRecognitionLanguage = language;

    const audioConfig = speechsdk.AudioConfig.fromDefaultMicrophoneInput();
    const recognizer = new speechsdk.ConversationTranscriber(
      speechConfig,
      audioConfig
    );
    setEngine(recognizer);
    return recognizer;
  };

  const handleChooseLanguage = (newLanguage: string) => {
    setLanguage(newLanguage as any);
    initializeSpeechEngine(newLanguage);
  };

  const { setConversation, setSummary, setLoading } = useAmbientStore();

  const handleTranscript = async (transcript: string) => {
    try {
      setCurrentMode(LOADING);
      setLoading(true);

      if (isAmbientRecord && source) {
        try {
          const ambientResponse = await summarizeAmbientListening(
            source,
            transcript
          );

          // Update the ambient store with the response
          if (ambientResponse.conversation) {
            setConversation(ambientResponse.conversation);
          }
          if (ambientResponse.summary) {
            setSummary(ambientResponse.summary);
          }

          // Also update local state if needed
          setResponse({
            conversation: ambientResponse.conversation || [],
            summary: ambientResponse.summary || {},
          });
        } catch (error) {
          console.error('Error in ambient listening:', error);
          toast.error('Error processing ambient data');
          throw error;
        } finally {
          setLoading(false);
        }
      } else {
        try {
          const consultationResponse = await summarizeConversation(transcript);
          setResponse(consultationResponse);
        } catch (error) {
          console.error('Error in conversation summary:', error);
          throw error;
        }
      }

      setCurrentMode(SHOW_SUMMARY);
    } catch (error) {
      if (!isAmbientRecord) {
        toast.error('Error in summarizing conversation');
      }
      setCurrentMode(LANGUAGE_SELECTION);
      setResponse(null);
      console.error('Error in handleTranscript:', error);
      setLoading(false);
    }
  };

  // Create a function to handle the save operation
  const handleSaveOperation = async () => {
    if (isAmbientRecord && source) {
      // For ambient records, we already have the data in response
      if (response?.summary) {
        await onSave(response.summary);
      }
    } else {
      // For non-ambient records, we need to submit the form
      await form.handleSubmit(async (submitted) => {
        if (response?.conversation) {
          await onSave({
            ...submitted,
            conversation: response.conversation,
          });
        } else {
          await onSave(submitted);
        }
      })();
    }
  };

  // The save handler that will be called when the save button is clicked
  const handleSave = async () => {
    try {
      await handleSaveOperation();
      // Only close the modal if save was successful
      handleClose();
    } catch (error) {
      console.error('Error saving record:', error);
      const errorMessage =
        isAmbientRecord && source
          ? 'Error saving ambient record'
          : 'Error saving record';
      toast.error(errorMessage);
      // Don't close the modal on error
    }
  };

  const getModalTitle = () => {
    switch (currentMode) {
      case LANGUAGE_SELECTION:
        return 'Choose Language';
      case RECORD_CONSULTATION:
        return 'Recording and live transcription';
      case LOADING:
        return 'Processing...';
      case SHOW_SUMMARY:
        return 'Consultation Summary';
      default:
        return 'Ambient Listening';
    }
  };

  useEffect(() => {
    if (doctorProfile?.id) {
      fetchCustomiseEmr(doctorProfile?.id as string);
    }
  }, [doctorProfile?.id, fetchCustomiseEmr]);

  useEffect(() => {
    if (doctorLanguage) {
      setLanguage(doctorLanguage);
    } else {
      setLanguage(RecordingLanguage.English);
    }
  }, [doctorLanguage]);

  useEffect(() => {
    if (currentMode === LANGUAGE_SELECTION) {
      setCurrentMode(LOADING);
      initializeSpeechEngine(language);
      setCurrentMode(LANGUAGE_SELECTION);
    }
  }, [currentMode, language]);

  return (
    <>
      <button
        className={`text-sm flex items-center justify-center gap-1.5 py-1.5 w-full rounded-md ${
          disabled
            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
            : 'bg-[#4B6BFB] text-white hover:bg-[#3a56d4]'
        }`}
        onClick={!disabled ? startFlow : undefined}
        disabled={disabled}
      >
        Ambient Listening
      </button>

      <AppModal
        open={isModalOpen}
        onClose={handleClose}
        title={
          currentMode === LANGUAGE_SELECTION ||
          currentMode === RECORD_CONSULTATION
            ? modalTitle || getModalTitle()
            : ''
        }
        classes={{
          root:
            currentMode === LANGUAGE_SELECTION
              ? ' max-h-[95vh]  overflow-auto'
              : 'max-h-[95vh] min-h-[65vh] overflow-auto min-w-[60vw]',
          body: '!p-0 flex flex-col flex-1 min-h-0',
        }}
      >
        <section className="flex flex-col flex-1 min-h-0 w-full">
          {(currentMode === LANGUAGE_SELECTION ||
            currentMode === RECORD_CONSULTATION) && (
            <RecordConsultation
              engine={engine}
              onRecordEnd={handleTranscript}
              handleChooseLanguage={handleChooseLanguage}
              selectedLanguage={language}
              currentMode={currentMode}
              setCurrentMode={setCurrentMode}
              isLanguageSelection={currentMode === LANGUAGE_SELECTION}
            />
          )}

          {currentMode === LOADING && (
            <div className="flex flex-1 min-h-[65vh] items-center justify-center w-full">
              <Loading />
            </div>
          )}

          {currentMode === SHOW_SUMMARY && (
            <>
              <div className="flex flex-col h-full max-h-[80vh] relative pb-16">
                <Tabs
                  defaultValue="conversation"
                  className="w-full flex flex-col flex-1 min-h-0"
                >
                  <div className="bg-white sticky top-0 z-10 py-5 pl-6.5 pr-7.5 text-xl font-semibold">
                    <TabsList className="flex w-full justify-between">
                      <TabsTrigger
                        className="flex-1 text-center"
                        value="summary"
                      >
                        {`${summaryFormTitle} Summary (Generated)`}
                      </TabsTrigger>
                      <TabsTrigger
                        className="flex-1 text-center"
                        value="conversation"
                      >
                        Transcription
                      </TabsTrigger>
                    </TabsList>
                  </div>

                  <div className="overflow-y-auto flex-1">
                    <TabsContent
                      className="pb-5 pl-6 pr-7.5 min-h-0"
                      value="summary"
                    >
                      {customSummaryForm ? (
                        <>{customSummaryForm}</>
                      ) : (
                        <SummaryForm data={response} form={form} editable />
                      )}
                    </TabsContent>

                    <TabsContent className="min-h-0" value="conversation">
                      <div className="pb-5 pl-6 pr-7.5">
                        {response ? (
                          <Transcript conversation={response?.conversation} />
                        ) : (
                          <div className="flex items-center justify-center text-gray-500 h-40">
                            Transcription not generated
                          </div>
                        )}
                      </div>
                    </TabsContent>
                  </div>
                </Tabs>

                <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-t-[#C2CDD6] z-20 shadow-md">
                  <div className="flex w-full px-6.5 py-3 justify-end gap-4">
                    <PrimaryButton
                      variant="outlined"
                      className="text-[16px] font-normal px-4 h-9 rounded-xl border-black text-black"
                      onClick={handleClose}
                    >
                      Close
                      <BiX />
                    </PrimaryButton>

                    <PrimaryButton
                      variant="contained"
                      className="bg-[#012436] text-white text-[16px] font-normal px-4 h-9 rounded-xl ml-auto"
                      onClick={handleSave}
                      disabled={isLoading}
                      isLoading={isLoading}
                    >
                      Save Record
                      <BiSave />
                    </PrimaryButton>
                  </div>
                </div>
              </div>
            </>
          )}
        </section>
      </AppModal>
    </>
  );
};

export default AddRecord;
