import { filterBy } from '@/utils/filter-by-util';

import { sortOrder } from '@/core/components/table/types';
import { api, arcaAxios } from '@/core/lib/interceptor';
import { LabTestItem } from '@/types/emr/lab';

export interface LabTestPayload {
  patientId?: string;
  labTests: LabTestItem[];
  doctorId?: string;
}

type PackagePayload = {
  name: string;
  id?: string | number;
  type: string | null;
  tests: {
    testId: string | number;
    testName: string;
  }[];
};

type UpdateTestPackagePayload = {
  packageId: string | number;
  updates: {
    name: string;
    type: string;
    tests: {
      testId: string | number;
      testName: string;
    }[];
  };
};

export type PatientLabTestParams = {
  patientId: string;
  dateFilter: string;
  sortField: string;
  sortOrder: string;
  customStartDate?: string;
  customEndDate?: string;
  department: string;
  searchText: string;
};

export type RecentTestParams = {
  patientId: string;
  startDate: string;
  endDate: string;
};

export const searchTests = async (
  searchTerm: string,
  selectedDepartment: string,
  organizationId?: string
) => {
  return await arcaAxios.post(`/lab-tests/search`, {
    pageSize: 1000, // Increased page size to 1000
    pageNumber: 1, // Always start from page 1
    searchText: searchTerm,
    department: selectedDepartment,
    ...(organizationId && { organizationId }),
  });
};

export const createNewTest = async (data: LabTestPayload) => {
  return await arcaAxios.post('/patient-lab-test', data);
};

export const updateLabTest = async (data: LabTestPayload & { id: string }) => {
  return await arcaAxios.patch(
    `/patient-lab-test?patientId=${data.patientId}`,
    data
  );
};

export const updateLabTestStatus = async (
  testIds: string[],
  status: string
) => {
  return await arcaAxios.patch('/patient-lab-test/status', {
    testIds,
    status,
  });
};

export const getLabDepartments = async () => {
  return await arcaAxios.get<string[]>(`/lab-test/departments`);
};

export const createTestPackage = async (
  data: PackagePayload,
  departmentType: string
) => {
  return await arcaAxios.post<PackagePayload>(
    `/test-package?type=${departmentType}`,
    data
  );
};

export const fetchTestPackages = async (type: string) => {
  return await arcaAxios.get(`/test-package`, {
    params: { type },
  });
};

export const fetchUserPackage = async (userId: string) => {
  return await api.get(`/test-package/v0.1/packages/user-specific`, {
    params: { userId },
  });
};

export const fetchTestsByPackageId = async (packageId: string) => {
  return await arcaAxios.get(`/package/tests`, {
    params: { packageId },
  });
};

export const updateTestPackage = async (data: UpdateTestPackagePayload) => {
  return await arcaAxios.patch('/test-package', data);
};

export const patientLabeTest = async (params: PatientLabTestParams) => {
  return await arcaAxios.get('/patient-lab-test', { params });
};

export const uploadTestResult = async (data: FormData) => {
  return await arcaAxios.postForm('/lab-report/upload', data);
};

export const previewTestResult = async (code: string): Promise<Blob> => {
  const { data } = await arcaAxios.get<Blob>(
    `lab-report/preview?docId=${code}`,
    {
      responseType: 'blob',
    }
  );

  return data;
};

export const patientRecentTestResults = async (params: RecentTestParams) => {
  const { patientId, startDate, endDate } = params;
  return await arcaAxios.get(`/patient-lab-test`, {
    params: {
      patientId: patientId,
      dateFilter: filterBy.CUSTOM_DATE,
      customStartDate: startDate,
      customEndDate: endDate,
      sortOrder: sortOrder.DESC,
      sortField: 'updated_on',
      searchText: '',
      department: 'ALL',
    },
  });
};
