import React, { useState, useEffect, useMemo, useCallback } from 'react';

import { useForm, useFieldArray } from 'react-hook-form';

import { Box } from '@mui/material';

import { cn } from '@/lib/utils';

import usePaymentPermissions from '@/hooks/usePaymentPermissions';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { usePrescriptionStore } from '@/store/emr/prescription';
import { usePaymentStore } from '@/store/payments';
import { useUserStore } from '@/store/userStore';

import { getDoctorName } from '@/utils/constants/lifeStyle';

import PaymentFailureModal from '@/views/mrd/payment/PaymentFailureModal';

import FieldValidationError from '@/emr/components/shared/FieldValidationError';

import PrimaryButton from '@/core/components/primary-button';
import {
  defaultPrescriptionRow,
  PrescriptionItem,
  PrescriptionModalType,
  prescriptionModalTypes,
  prescriptionMode,
  prescriptionTabs,
} from '@/types/emr/prescription';

import PrescriptionTable from './PrescriptionTable';

import { ClearConfirmationModal } from './shared/ClearModal';
import PrescriptionPaymentConfirmationModal from './shared/PrescriptionPaymentConfirmationModal';
import PrescriptionPaymentSuccessModal from './shared/PrescriptionPaymentSuccessModal';
import ConfirmationModal from './shared/SaveConfirmationModal';

export type Prescription = {
  prescription: PrescriptionItem[];
};

type Props = {
  tableHeight?: string;
};

const { PRESCRIPTION_HISTORY } = prescriptionTabs;
const { SAVE, CLEAR } = prescriptionModalTypes;
const { VIEW, NEW } = prescriptionMode;

const NewPrescription: React.FC<Props> = () => {
  const {
    selectedMedicines,
    clearSelectedMedicines,
    removeSelectedMedicine,
    setActiveTab,
    createPrescription,
    updatePrescription,
    setSelectedMedicines,
    onExpand,
    prescriptionMode: mode,
    selectedHistory: prescription,
    isLoading,
    showPaymentConfirmationModal,
    showPaymentSuccessModal,
    isProcessingPayment,
    setShowPaymentConfirmationModal,
    setShowPaymentSuccessModal,
    setIsProcessingPayment,
  } = usePrescriptionStore();
  const { name } = useUserStore();
  const { patient } = useCurrentPatientStore();
  const doctor = useDoctorStore((state) => state.doctorProfile);
  const { data: userData } = useUserStore();
  const { isPaymentEnabled } = usePaymentPermissions();

  const {
    createOrder,
    openRazorpayPayment,
    handlePaymentFailure,
    resetPaymentState,
    showFailureModal,
    setShowFailureModal,
  } = usePaymentStore();

  const isPrescriptionPaymentEnabled = isPaymentEnabled('prescriptionEnabled');

  const [modalType, setModalType] = useState<PrescriptionModalType | null>(
    null
  );

  const defaultValues = useMemo(
    () => ({
      prescription:
        mode !== NEW ? prescription?.medicines : [defaultPrescriptionRow],
    }),
    [mode, prescription?.medicines]
  );

  const {
    control,
    handleSubmit,
    clearErrors,
    reset,
    trigger,
    watch,
    setValue,
    getValues,
    formState: { errors },
  } = useForm<Prescription>({
    defaultValues,
    mode: 'onChange',
    reValidateMode: 'onChange',
  });

  const { fields, remove, update } = useFieldArray({
    control,
    name: 'prescription',
  });
  const watchPrescription = watch('prescription');
  const [totalCostState, setTotalCostState] = useState('00.00');

  // Calculate total cost and update state immediately
  const calculateTotalCost = useCallback(() => {
    const currentPrescription = getValues('prescription');
    if (!currentPrescription || currentPrescription.length === 0) {
      setTotalCostState('00.00');
      return '00.00';
    }

    const total = currentPrescription.reduce((sum, item, index) => {
      // Get cost from either cost or Cost property, default to '0' if both are undefined
      const costValue =
        item.cost !== undefined
          ? item.cost
          : (item as any).Cost !== undefined
            ? (item as any).Cost
            : '0';

      // Convert to number, handling both string and number types
      const cost =
        typeof costValue === 'string'
          ? parseFloat(costValue) || 0
          : Number(costValue) || 0;

      console.log(`Row ${index}: costValue=${costValue}, parsed=${cost}`);
      return sum + cost;
    }, 0);

    const totalFormatted = total.toFixed(2);
    console.log(`Total cost calculated: ${totalFormatted}`);
    setTotalCostState(totalFormatted);
    return totalFormatted;
  }, [getValues]);

  // Use the state-based total cost
  const totalCost = totalCostState;

  // Update total cost when watchPrescription changes
  useEffect(() => {
    calculateTotalCost();
  }, [watchPrescription, calculateTotalCost]);

  // Enhanced cost update handler to ensure immediate total cost recalculation
  useEffect(() => {
    const subscription = watch((formData, { name, type }) => {
      if (
        type === 'change' &&
        name &&
        (name.includes('.quantity') || name.includes('.cost'))
      ) {
        // Immediately recalculate total cost when any cost or quantity changes
        setTimeout(() => {
          calculateTotalCost();
        }, 0);

        // Validate that all cost calculations are correct
        const currentValues = getValues('prescription');
        currentValues.forEach((item, index) => {
          if (item.quantity && selectedMedicines[index]?.Cost) {
            const quantity = parseFloat(item.quantity);
            const costPerUnit = parseFloat(
              String(selectedMedicines[index].Cost)
            );
            const expectedCost = (quantity * costPerUnit).toFixed(2);

            if (
              item.cost !== expectedCost &&
              !isNaN(quantity) &&
              !isNaN(costPerUnit)
            ) {
              setValue(`prescription.${index}.cost`, expectedCost, {
                shouldValidate: true,
                shouldDirty: true,
                shouldTouch: true,
              });
              // Recalculate total after updating cost
              setTimeout(() => {
                calculateTotalCost();
              }, 10);
            }
          }
        });
      }
    });

    return () => subscription.unsubscribe();
  }, [watch, getValues, setValue, selectedMedicines, calculateTotalCost]);

  const isReadOnly = useMemo(() => mode === VIEW, [mode]);

  const handleRemove = useCallback(
    (index: number, id: string) => {
      if (fields.length === 1) {
        update(index, defaultPrescriptionRow);
      } else {
        remove(index);
      }
      removeSelectedMedicine(id);
    },
    [fields.length, remove, removeSelectedMedicine, update]
  );

  const onSubmit = useCallback(
    async (data: Prescription) => {
      const doctorName = getDoctorName(doctor?.general?.fullName, name);
      const payload = {
        patientId: patient?.id,
        doctor: doctorName,
        medicines: data.prescription ?? [],
        doctorEmail: userData.email,
        status: 'Unpaid' as const, // Save without payment = Unpaid status
      };

      if (prescription?.id) {
        return await updatePrescription(prescription.id, payload);
      } else {
        const response = await createPrescription(payload);
        reset({ prescription: [defaultPrescriptionRow] });
        clearSelectedMedicines();
        return response;
      }
    },
    [
      doctor?.general?.fullName,
      name,
      patient?.id,
      userData?.email,
      prescription?.id,
      updatePrescription,
      createPrescription,
      reset,
      clearSelectedMedicines,
    ]
  );

  const _handleSaveClick = useCallback(async () => {
    const isValid = await trigger('prescription');
    if (!isValid) {
      setValue('prescription', [...watchPrescription], {
        shouldValidate: true,
        shouldDirty: true,
        shouldTouch: true,
      });
    } else {
      setModalType(SAVE);
    }
  }, [trigger, setValue, watchPrescription]);

  const handleClearClick = useCallback(() => {
    setModalType(CLEAR);
  }, []);

  const handleSaveConfirm = useCallback(() => {
    const currentPrescriptions = getValues('prescription');
    const isDefaultPrescriptionOnly =
      currentPrescriptions.length === 1 &&
      Object.keys(defaultPrescriptionRow).every(
        (key) => currentPrescriptions[0][key] === defaultPrescriptionRow[key]
      );

    if (!isDefaultPrescriptionOnly) {
      handleSubmit(async (data) => {
        const response = await onSubmit(data);
        setModalType(null);
        setActiveTab(PRESCRIPTION_HISTORY);
        response && onExpand(response);
      })();
    } else {
      setModalType(null);
    }
  }, [getValues, handleSubmit, onSubmit, setActiveTab, onExpand]);

  const handleClearConfirm = useCallback(() => {
    reset({ prescription: [defaultPrescriptionRow] });
    setModalType(null);
    clearSelectedMedicines();
  }, [reset, clearSelectedMedicines]);

  const handleCancel = useCallback(() => {
    setModalType(null);
  }, []);

  const handleProceedToPaymentClick = useCallback(async () => {
    const isValid = await trigger('prescription');
    if (!isValid) {
      setValue('prescription', [...watchPrescription], {
        shouldValidate: true,
        shouldDirty: true,
        shouldTouch: true,
      });

      return;
    }

    setShowPaymentConfirmationModal(true);
  }, [setShowPaymentConfirmationModal, trigger, setValue, watchPrescription]);

  const handlePaymentModalSave = useCallback(() => {
    setShowPaymentConfirmationModal(false);
    handleSaveConfirm();
  }, [setShowPaymentConfirmationModal, handleSaveConfirm]);

  const handlePaymentModalClose = useCallback(() => {
    setShowPaymentConfirmationModal(false);
  }, [setShowPaymentConfirmationModal]);

  const handleProceedToPay = useCallback(async () => {
    console.log('handleProceedToPay called - starting payment flow');

    setShowPaymentConfirmationModal(false);

    const currentPrescriptions = getValues('prescription');

    if (!patient?.id || !userData?.organizationId) {
      console.error('Missing patient ID or organization ID', {
        patientId: patient?.id,
        organizationId: userData?.organizationId,
      });
      setIsProcessingPayment(false);
      return;
    }

    try {
      setIsProcessingPayment(true);

      const doctorName = getDoctorName(doctor?.general?.fullName, name);

      const paymentData = {
        amount: parseFloat(totalCost),
        currency: 'INR',
        paymentType: 'prescription',
        organizationId: userData.organizationId,
        patientId: patient.id,
        description: 'Prescription Medicines',
        metadata: {},
      };

      console.log('Creating payment order with data:', paymentData);
      const order = await createOrder(paymentData);
      console.log('Payment order created:', order);

      if (!order) {
        throw new Error('Failed to create payment order');
      }

      setIsProcessingPayment(false);

      await openRazorpayPayment(
        order,
        patient.name || 'Patient',
        async (response) => {
          console.log('Payment successful:', response);

          setIsProcessingPayment(false);
          setActiveTab(PRESCRIPTION_HISTORY);

          try {
            const prescriptionPayload = {
              patientId: patient.id,
              doctor: doctorName,
              medicines: currentPrescriptions,
              doctorEmail: userData.email,
              status: 'Paid' as const,
            };

            let prescriptionData;

            if (prescription?.id) {
              prescriptionData = await updatePrescription(
                prescription.id,
                prescriptionPayload
              );
            } else {
              prescriptionData = await createPrescription(
                prescriptionPayload,
                false
              );
            }
            if (!prescriptionData) {
              throw new Error('Failed to save prescription after payment');
            }

            prescriptionData && onExpand(prescriptionData);

            reset({ prescription: [defaultPrescriptionRow] });
            clearSelectedMedicines();

            if (!prescription?.id) {
              setTimeout(() => {
                setShowPaymentSuccessModal(true);
              }, 100);
            }
          } catch (error) {
            console.error('Failed to save prescription after payment:', error);

            handlePaymentFailure(
              'Payment successful but failed to save prescription'
            );
          }
        },
        (error) => {
          console.error('Payment failed:', error);
          setIsProcessingPayment(false);
          handlePaymentFailure(error.message || 'Payment failed');
        }
      );
    } catch (error) {
      console.error('Error in payment flow:', error);
      setIsProcessingPayment(false);
      handlePaymentFailure('Failed to process payment');
    }
  }, [
    getValues,
    patient,
    userData,
    doctor,
    name,
    totalCost,
    setIsProcessingPayment,
    createPrescription,
    updatePrescription,
    prescription?.id,
    createOrder,
    openRazorpayPayment,
    setShowPaymentConfirmationModal,
    setShowPaymentSuccessModal,
    setActiveTab,
    onExpand,
    handlePaymentFailure,
    reset,
    clearSelectedMedicines,
  ]);

  const handlePaymentSuccessClose = useCallback(() => {
    setShowPaymentSuccessModal(false);
    resetPaymentState();
  }, [setShowPaymentSuccessModal, resetPaymentState]);

  const handlePaymentFailureClose = useCallback(() => {
    setShowFailureModal(false);
  }, [setShowFailureModal]);

  const handleRetryPayment = useCallback(() => {
    setShowFailureModal(false);
    handleProceedToPay();
  }, [setShowFailureModal, handleProceedToPay]);

  useEffect(() => {
    if (mode === NEW) {
      setSelectedMedicines([]);
      return;
    }

    if (!prescription?.medicines) return;

    const itemsForStore = prescription.medicines.map((item) => {
      const numQuantity = parseFloat(String(item.quantity));
      const numTotalCost = parseFloat(String(item.cost));
      const unitCost =
        numQuantity > 0 && !isNaN(numTotalCost) && isFinite(numQuantity)
          ? (numTotalCost / numQuantity).toFixed(2)
          : '0';

      return { ...item, Cost: unitCost };
    });

    setSelectedMedicines(itemsForStore);
  }, [prescription?.medicines, setSelectedMedicines, mode]);

  useEffect(() => {
    if (selectedMedicines.length > 0) {
      const currentPrescription = getValues('prescription');

      const formattedMedicines = selectedMedicines.map((medicine) => {
        const isMedicineAlreadyInPrescription = currentPrescription.find(
          (item) => item.id === medicine.id
        );

        if (isMedicineAlreadyInPrescription) {
          return isMedicineAlreadyInPrescription;
        }
        const quantity = parseFloat(medicine.quantity ?? '1');
        const costPerUnit = parseFloat(String(medicine.Cost || '0'));
        const totalCost =
          !isNaN(quantity) && !isNaN(costPerUnit)
            ? (quantity * costPerUnit).toFixed(2)
            : '0';

        return {
          ...medicine,
          drugForm: medicine.DrugFormulation,
          genericName: medicine.GenericName,
          brandName: medicine.BrandName,
          strength: medicine.Strength,
          measure: medicine.Measure,
          uom: medicine.UnitOfMeasure,
          quantity: medicine.quantity ?? '1',
          cost: totalCost,
        };
      });

      reset({ prescription: formattedMedicines });
    }
  }, [selectedMedicines, reset, getValues]);

  useEffect(() => {
    if (mode === VIEW && prescription?.medicines) {
      reset(
        { prescription: prescription.medicines },
        { keepDefaultValues: false }
      );
    }
  }, [prescription?.medicines, mode, reset]);

  useEffect(() => {
    const handleFieldChange = (name: string, value: any) => {
      if (!name) return;

      // Handle other field changes (instruction, duration, frequency, route)
      const fieldMatch = name.match(
        /^prescription\.(\d+)\.(instruction|duration|frequency|route)$/
      );
      if (fieldMatch) {
        const index = parseInt(fieldMatch[1]);
        const field = fieldMatch[2];

        if (value !== undefined && value !== null && value !== '') {
          // Use setTimeout to defer the validation to the next tick
          setTimeout(() => {
            trigger(`prescription.${index}.${field}`);
          }, 0);
        }
      }
    };

    const subscription = watch((_value, { name, type }) => {
      if (type === 'change' && name) {
        handleFieldChange(name, getValues(name));
      }
    });

    return () => subscription.unsubscribe();
  }, [watch, trigger, getValues, setValue, selectedMedicines, clearErrors]);

  useEffect(() => {
    return () => {
      reset({ prescription: [defaultPrescriptionRow] });
    };
  }, [reset]);

  useEffect(() => {
    const subscription = watch((_value, { name }) => {
      if (name?.startsWith('prescription.')) {
        const fieldValue = getValues(name);
        if (
          fieldValue !== undefined &&
          fieldValue !== null &&
          fieldValue !== ''
        ) {
          clearErrors(name);
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [watch, getValues, clearErrors]);

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="flex flex-col h-full relative"
    >
      <div className="w-full min-w-0 overflow-x-auto flex-1 mb-2 pl-2 pb-16">
        <PrescriptionTable
          fields={fields}
          control={control}
          isReadOnly={isReadOnly}
          selectedMedicines={selectedMedicines}
          handleRemove={handleRemove}
          clearErrors={clearErrors}
          setValue={setValue}
          calculateTotalCost={calculateTotalCost}
        />
        <FieldValidationError errors={errors} fieldKey="prescription" />
      </div>

      <Box
        className={cn('fixed bottom-10 left-105 z-50', {
          hidden: mode === VIEW,
        })}
      >
        <div className="capitalize px-8 text-black-600 bg-white border border-gray-400 text-sm py-2 rounded">
          Total Cost: ₹ {totalCost}
        </div>
      </Box>

      <Box
        gap={3}
        className={cn('fixed bottom-10 right-80 flex justify-end z-50', {
          hidden: mode === VIEW,
        })}
      >
        <PrimaryButton
          type="button"
          className="capitalize px-8  text-gray-600 bg-white border border-gray-400 text-sm hover:bg-gray-50"
          onClick={handleClearClick}
        >
          Clear All
        </PrimaryButton>
        {isPrescriptionPaymentEnabled ? (
          <PrimaryButton
            type="button"
            onClick={handleProceedToPaymentClick}
            className="capitalize px-8  text-white bg-black border border-black text-sm hover:bg-black-100"
          >
            Proceed to Payment
          </PrimaryButton>
        ) : (
          <PrimaryButton
            type="button"
            onClick={_handleSaveClick}
            className="capitalize px-8  text-white bg-gray-600 border border-gray-600 text-sm hover:bg-gray-700"
          >
            Save
          </PrimaryButton>
        )}
        <ConfirmationModal
          open={modalType === 'save'}
          onConfirm={handleSaveConfirm}
          onCancel={handleCancel}
          isLoading={isLoading}
        />
        <ClearConfirmationModal
          open={modalType === 'clear'}
          onConfirm={handleClearConfirm}
          onCancel={handleCancel}
        />
        <PrescriptionPaymentConfirmationModal
          open={showPaymentConfirmationModal}
          onClose={handlePaymentModalClose}
          onSave={handlePaymentModalSave}
          onProceedToPay={handleProceedToPay}
          loading={isProcessingPayment}
          totalCost={totalCost}
        />
        <PrescriptionPaymentSuccessModal
          open={showPaymentSuccessModal}
          onClose={handlePaymentSuccessClose}
        />
        <PaymentFailureModal
          open={showFailureModal}
          onClose={handlePaymentFailureClose}
          onRetry={handleRetryPayment}
          showCloseButton={false}
          errorMessage={
            <>
              Couldn&apos;t save your prescription
              <br />
              due to technical failure.
              <br />
              Try again later.
            </>
          }
        />
      </Box>
    </form>
  );
};

export default NewPrescription;
