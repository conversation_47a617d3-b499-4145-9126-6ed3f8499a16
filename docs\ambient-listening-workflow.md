# Ambient Listening Workflow Documentation

## Overview

Ambient Listening is a feature in the ARCA EMR application that allows doctors to record consultations, automatically transcribe them using AI, and extract structured medical information that can be saved to various lifestyle modules (nutrition, physical activity, etc.).

## 🎯 Purpose

- **Record doctor-patient conversations** during consultations
- **Automatically transcribe** speech to text in real-time
- **Extract structured medical data** using AI processing
- **Save information** to appropriate lifestyle modules
- **Reduce manual data entry** for doctors

---

## 🔄 Complete Workflow

### Phase 1: Initiation
```
Doctor clicks "Ambient Listening" → Language Selection Modal Opens
```

### Phase 2: Setup
```
Doctor selects language → Speech engine initializes → Recording UI appears
```

### Phase 3: Recording
```
Doctor starts recording → Real-time transcription → Live transcript display
```

### Phase 4: Processing
```
Doctor ends recording → Transcript sent to AI → Medical summary generated
```

### Phase 5: Review & Save
```
Summary displayed → Doctor reviews → Saves to lifestyle module
```

---

## 📱 User Interface Flow

### 1. **Entry Point**
- **Location**: Lifestyle sidebar
- **Component**: `AmbientListening.tsx` (button)
- **Action**: Opens ambient listening modal

### 2. **Language Selection**
- **Modal State**: `LANGUAGE_SELECTION`
- **Available Languages**: 
  - English, Malayalam, Tamil, Kannada, Telugu, Bengali, Hindi
- **User Action**: Select preferred language
- **Next**: Initialize speech engine

### 3. **Recording Interface**
- **Modal State**: `RECORD_CONSULTATION`
- **Features**:
  - Visual sine wave animation (shows recording is active)
  - Live transcript display with auto-scroll
  - Recording controls (Start/Pause/Resume/End)
  - Real-time speech-to-text conversion

### 4. **Processing State**
- **Modal State**: `LOADING`
- **Process**: 
  - Transcript sent to backend API
  - AI processes the conversation
  - Extracts medical information
- **Duration**: Usually 10-30 seconds

### 5. **Summary Review**
- **Modal State**: `SHOW_SUMMARY`
- **Display**: 
  - Structured conversation (Doctor vs Patient)
  - Extracted medical summary
  - Form fields pre-populated with AI data
- **Actions**: Review, edit, and save

---

## 🔧 Technical Implementation

### Frontend Architecture

#### Core Components
```
src/lib/add_record/
├── index.tsx                 # Main ambient listening logic
└── modals/
    └── RecordConsultation.tsx # Recording UI and controls
```

#### Key Technologies
- **Azure Speech SDK**: `microsoft-cognitiveservices-speech-sdk`
- **Speech Engine**: `ConversationTranscriber`
- **State Management**: Zustand stores
- **UI Framework**: React with TypeScript

#### Recording States
```typescript
enum RecordingState {
  IDLE = 'idle',           // Not recording
  RECORDING = 'recording', // Actively recording
  PAUSED = 'paused'        // Recording paused
}
```

#### Modal States
```typescript
enum CurrentModal {
  LANGUAGE_SELECTION = 'language-selection',
  RECORD_CONSULTATION = 'record-consultation', 
  LOADING = 'loading',
  SHOW_SUMMARY = 'show-summary'
}
```

### Backend Processing

#### API Endpoint
```
POST /lifestyle/v0.1/lifestyle/ambient-listening
```

#### Request Payload
```json
{
  "source": "nutrition_practice_food_intake_patterns",
  "transcript": "Doctor: How many meals do you eat per day? Patient: I usually eat 3 meals..."
}
```

#### AI Processing Steps
1. **Speaker Identification**: AI identifies who is speaking (doctor vs patient)
2. **Medical Information Extraction**: Extracts relevant medical data
3. **Structured Response**: Returns organized conversation and summary

#### Response Format
```json
{
  "conversation": [
    {"speaker": "doctor", "message": "How many meals do you eat per day?"},
    {"speaker": "patient", "message": "I usually eat 3 meals..."}
  ],
  "summary": {
    "diethistory": "Patient eats 3 meals per day...",
    "physicalactivityhistory": "Patient exercises 2 times per week..."
  }
}
```

---

## 🗂️ Integration with Lifestyle Modules

### Supported Modules
1. **Nutrition Practice - Food Intake Patterns**
2. **Nutrition Practice - Dietary Recall**
3. **Nutrition Practice - Food Frequency Questionnaire**
4. **Nutrition Attitude**
5. **Physical Activity Practice - Exercise Patterns**
6. **Physical Activity Attitude**
7. **Physical Activity Knowledge**

### Data Mapping Process
```typescript
// AI extracts data and maps to form fields
const summary = {
  diethistory: "Patient eats 3 meals per day",
  physicalactivityhistory: "Exercises twice weekly"
};

// Data is pre-populated in the appropriate form
<FoodIntakePatternModal 
  initialValues={summary}
  mode="CREATE" 
/>
```

### Save Process
1. **Review**: Doctor reviews AI-generated data
2. **Edit**: Doctor can modify any field
3. **Save**: Data saved to selected lifestyle module
4. **Storage**: Stored in patient's lifestyle records

---

## 🎛️ Key Features

### Real-time Transcription
- **Live Display**: Transcript appears as conversation happens
- **Auto-scroll**: Automatically scrolls to show latest text
- **History**: Maintains transcript history during session

### Visual Feedback
- **Sine Wave Animation**: Shows recording is active
- **Recording Status**: Clear visual indicators
- **Progress States**: Loading animations during processing

### Multi-language Support
- **7 Languages**: English, Malayalam, Tamil, Kannada, Telugu, Bengali, Hindi
- **Language Persistence**: Remembers doctor's preference
- **Regional Support**: Optimized for Indian languages

### Smart AI Processing
- **Speaker Recognition**: Distinguishes doctor from patient
- **Medical Context**: Understands medical terminology
- **Structured Extraction**: Organizes information by category

---

## 📊 Data Flow Diagram

```
[Doctor] → [Language Selection] → [Speech Engine] → [Recording]
    ↓
[Real-time Transcription] → [Live Display] → [End Recording]
    ↓
[Send to Backend] → [AI Processing] → [Speaker Identification]
    ↓
[Medical Extraction] → [Structured Summary] → [Form Pre-population]
    ↓
[Doctor Review] → [Edit if needed] → [Save to Lifestyle Module]
```

---

## 🔒 Privacy & Security

### Audio Handling
- **No Storage**: Audio is not stored on servers
- **Real-time Processing**: Converted to text immediately
- **Secure Transmission**: Encrypted communication with Azure

### Data Protection
- **Patient Privacy**: All data encrypted and secure
- **Access Control**: Only authorized doctors can access
- **Audit Trail**: All actions logged for compliance

---

## 🚀 Benefits for Medical Practice

### For Doctors
- **Reduced Documentation Time**: Automatic data entry
- **Better Patient Focus**: Less time typing, more time listening
- **Accurate Records**: AI ensures comprehensive documentation
- **Multi-language Support**: Works with diverse patient populations

### For Patients
- **Better Engagement**: Doctor maintains eye contact
- **Comprehensive Care**: Nothing gets missed in documentation
- **Faster Consultations**: Efficient data capture process

### For Healthcare System
- **Improved Efficiency**: Faster consultation processing
- **Better Data Quality**: Consistent, structured information
- **Cost Reduction**: Less administrative overhead
- **Compliance**: Automated documentation standards

---

## 🔧 Troubleshooting

### Common Issues
1. **Microphone Access**: Ensure browser permissions granted
2. **Network Issues**: Stable internet required for AI processing
3. **Language Recognition**: Speak clearly for better accuracy
4. **Background Noise**: Minimize ambient noise for best results

### Error Handling
- **Graceful Failures**: System handles errors without data loss
- **Retry Mechanisms**: Automatic retry for network issues
- **User Feedback**: Clear error messages and guidance
- **Fallback Options**: Manual entry always available

---

This ambient listening feature represents a significant advancement in medical documentation, combining cutting-edge AI technology with practical healthcare workflows to improve both doctor efficiency and patient care quality.
