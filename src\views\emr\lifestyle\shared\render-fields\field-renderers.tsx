import React, { Fragment, useCallback } from 'react';

import { Controller } from 'react-hook-form';

import { TextareaAutosize, Box, Typography } from '@mui/material';

import { cn } from '@/lib/utils';

import { allowNumbersWithDecimals } from '@/utils/validation';

import PenIcon from '@/assets/svg/PenIcon';

import { LifestyleFields } from '@/constants/emr/lifestyle/lifestyle-fields';

import AppRadio from '@/core/components/app-radio';
import AppSlider from '@/core/components/app-slider';
import AppTextField from '@/core/components/app-text-field';
import {
  RadioField as IRadioField,
  SliderField as ISliderField,
  ConditionalField as IConditionalField,
  NumberField as INumberField,
  TextField as ITextField,
  SectionField as ISectionField,
} from '@/types/emr/lifestyle/questionnaire';

import { GroupedTableField } from './GroupedTableField';
import { TableField } from './TableField';
import { FieldComponentProps } from './types';

export const RadioField: React.FC<FieldComponentProps> = ({
  name,
  field,
  control,
  errors,
  readonly,
  fieldIndex = 0,
  variant,
  sectionId,
}) => {
  const radioField = field as IRadioField;
  const radioOptions = radioField.options.map((option: string) => ({
    value: option,
    label: option,
  }));

  // Extract custom styles from field
  const hasCustomStyles = !!(field as any).customStyles;
  const customStyles = (field as any).customStyles || {};

  const isMainAttitudeQuestion =
    field.id === 'eating_healthy_food' || field.id === 'exercise_preference';

  // Check if this is the specific meal skip question that should be excluded
  const isMealSkipQuestion = field.label
    ?.toLowerCase()
    .includes('which meal do you skip');

  const isKnowledgeQuestion =
    !isMealSkipQuestion &&
    (field.label?.toLowerCase().includes('benefit of exercise') ||
      field.label?.toLowerCase().includes('examples of') ||
      field.label?.toLowerCase().includes('training') ||
      field.label?.toLowerCase().includes('exercise') ||
      field.label?.toLowerCase().includes('walking') ||
      field.label?.toLowerCase().includes('material') ||
      field.label?.toLowerCase().includes('food') ||
      field.label?.toLowerCase().includes('minimum') ||
      field.label?.toLowerCase().includes('which') ||
      field.label?.toLowerCase().includes('what is') ||
      field.label?.toLowerCase().includes('high carbohydrate content') ||
      field.label?.toLowerCase().includes('substitute for ragi') ||
      field.label?.toLowerCase().includes('processed foods') ||
      field.label?.toLowerCase().includes('high protein content') ||
      field.label?.toLowerCase().includes('high unsaturated fat content') ||
      field.label?.toLowerCase().includes('high saturated fat content') ||
      field.label?.toLowerCase().includes('healthy fat') ||
      field.label?.toLowerCase().includes('vitamin & mineral content') ||
      field.label?.toLowerCase().includes('high in fibre content') ||
      field.label?.toLowerCase().includes('high in salt content') ||
      field.label?.toLowerCase().includes('best cooking method') ||
      field.label?.toLowerCase().includes('expensive with marginal benefits') ||
      field.label?.toLowerCase().includes('associated with longevity') ||
      field.label?.toLowerCase().includes('most important meal') ||
      field.label?.toLowerCase().includes('the risk for heart disease') ||
      field.label?.toLowerCase().includes('recommended water intake') ||
      field.label
        ?.toLowerCase()
        .includes('recommended daily servings of fruits & vegetables') ||
      field.label?.toLowerCase().includes('high fibre intake prevents all') ||
      field.label?.toLowerCase().includes('high salt intake increases') ||
      field.label?.toLowerCase().includes('artificial sweeteners') ||
      field.label?.toLowerCase().includes('whole plant foods'));

  if (readonly) {
    // Special handling for modal variant: show both radio options with the selected one marked
    const isExerciseLikeQuestion = field.label
      ?.toLowerCase()
      .includes('do you like to exercise');
    const isEatingHealthyLikeQuestion =
      (field.label?.toLowerCase().includes('do you like eating healthy food') ??
        false) ||
      field.id === 'eating_healthy_food';

    if (
      variant === 'modal' &&
      (isExerciseLikeQuestion || isEatingHealthyLikeQuestion)
    ) {
      return (
        <Controller
          name={name}
          control={control}
          render={({ field: { value } }) => {
            const fieldValue =
              (field as any).value !== undefined ? (field as any).value : value;

            return (
              <div
                className="border-t border-b border-gray-300"
                style={{ borderColor: '#DAE1E7' }}
              >
                <div className="flex items-center py-3 px-4 bg-white">
                  <span
                    className="font-bold text-sm mr-8"
                    style={{
                      fontSize: '14px',
                      fontWeight: 700,
                      whiteSpace: 'nowrap',
                    }}
                  >
                    {field.label}
                  </span>
                  <div className="flex items-center gap-4">
                    {radioField.options.map((option) => (
                      <label key={option} className="flex items-center gap-2">
                        <input
                          type="radio"
                          name={name}
                          value={option}
                          checked={fieldValue === option}
                          readOnly
                          className="w-4 h-4 border-gray-300 focus:ring-black text-black"
                          style={{
                            accentColor: '#000000',
                            color: '#000000',
                          }}
                        />
                        <span className="text-sm">{option}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            );
          }}
        />
      );
    }

    if (isMainAttitudeQuestion) {
      return (
        <Controller
          name={name}
          control={control}
          render={({ field: { value } }) => {
            const fieldValue =
              (field as any).value !== undefined ? (field as any).value : value;

            return (
              <div className="flex items-center gap-4">
                <span className="text-base font-medium">{field.label}</span>
                <span
                  className={`text-base ${variant === 'timeline' ? 'font-normal' : 'font-semibold'}`}
                >
                  {fieldValue || 'No answer provided'}
                </span>
              </div>
            );
          }}
        />
      );
    }

    if (isKnowledgeQuestion) {
      return (
        <Controller
          name={name}
          control={control}
          render={({ field: { value } }) => {
            const fieldValue =
              (field as any).value !== undefined ? (field as any).value : value;

            const isEvenRow = fieldIndex % 2 === 0;
            const rowBgColor = isEvenRow ? 'bg-gray-100' : 'bg-white';

            return (
              <div
                className={`grid grid-cols-5 gap-0 border border-gray-300 divide-x divide-gray-300 min-h-[60px] items-start ${rowBgColor}`}
              >
                <div className="p-2 flex items-start">
                  <span className="text-sm text-gray-700 leading-relaxed break-words">
                    {field.label}
                  </span>
                </div>

                {[0, 1, 2, 3].map((optionIndex) => {
                  const option = radioOptions[optionIndex];
                  const isChecked = fieldValue === option?.value;

                  return (
                    <div
                      key={optionIndex}
                      className=" flex justify-center w-full h-full"
                      style={{ paddingTop: '12px' }}
                    >
                      {option ? (
                        <label className="flex items-start cursor-default w-[140px]">
                          <span
                            className={`text-sm leading-tight break-words ml-2 ${
                              isChecked
                                ? 'font-bold text-black'
                                : 'text-gray-600'
                            }`}
                          >
                            {option.label}
                          </span>
                        </label>
                      ) : (
                        <div className="w-4 h-4"></div>
                      )}
                    </div>
                  );
                })}
              </div>
            );
          }}
        />
      );
    }

    // Check if this is a food intake form in read-only mode
    const isFoodIntake = sectionId === 'food_intake';

    if (isFoodIntake) {
      return (
        <Controller
          name={name}
          control={control}
          render={({ field: { value } }) => {
            const fieldValue =
              (field as any).value !== undefined ? (field as any).value : value;

            return (
              <div className="w-full">
                <div className="flex flex-col">
                  <span className="text-base font-medium">
                    Q. {field.label}
                  </span>
                  <span className="text-base">
                    A:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    {fieldValue || 'No answer provided'}
                  </span>
                </div>
              </div>
            );
          }}
        />
      );
    }

    return (
      <Controller
        name={name}
        control={control}
        render={({ field: { value } }) => {
          const fieldValue =
            (field as any).value !== undefined ? (field as any).value : value;

          return (
            <div className="flex flex-col">
              <span className="text-base font-medium">Q. {field.label}</span>
              <span className="text-base">
                A:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                {fieldValue || 'No answer provided'}
              </span>
            </div>
          );
        }}
      />
    );
  }

  const isMainExerciseQuestion = field.label
    ?.toLowerCase()
    .includes('do you like to exercise');

  const defaultFormControlProps = {
    sx: {
      width: '100%',
      display: 'flex',
      flexDirection: 'row',
      gap: isMainExerciseQuestion ? 1 : isKnowledgeQuestion ? 0.5 : 2,
      alignItems: 'center',
      minHeight: isKnowledgeQuestion ? '32px' : 'auto',
      ['& .app-input-label']: {
        minWidth: isKnowledgeQuestion ? 250 : 400,
        maxWidth: isKnowledgeQuestion ? 250 : 'none',
        fontSize: isKnowledgeQuestion ? '13px' : '16px',
        lineHeight: isKnowledgeQuestion ? '1.2' : 'normal',
        ...(isMainExerciseQuestion && {
          fontWeight: 'bold',
          marginBottom: '4px',
        }),
        ...(isKnowledgeQuestion && {
          fontWeight: '400',
          marginBottom: '0px',
          paddingRight: '8px',
        }),
      },
      ['& .MuiRadioGroup-root']: {
        gap: isMainExerciseQuestion
          ? 1
          : isKnowledgeQuestion
            ? 0.5
            : hasCustomStyles
              ? customStyles.radioGroupGap
              : 2,

        ...(isKnowledgeQuestion && {
          gap: '4px',
          flexWrap: 'wrap',
        }),
      },
      ['& .MuiFormControlLabel-root']: {
        ...(isKnowledgeQuestion && {
          margin: '0 8px 0 0',
          '& .MuiFormControlLabel-label': {
            fontSize: '12px',
            lineHeight: '1.2',
          },
        }),
      },
    },
  };

  const formControlProps =
    (radioField as any).formControlProps || defaultFormControlProps;
  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange, value } }) => {
        const fieldValue =
          readonly && (field as any).value !== undefined
            ? (field as any).value
            : value;

        const radioComponent = (
          <AppRadio
            name={name}
            value={fieldValue}
            onChange={onChange}
            label={field.label}
            options={radioOptions}
            errors={errors?.[name]}
            required={isMainExerciseQuestion ? false : field.required}
            disabled={readonly}
            formControlProps={formControlProps}
          />
        );

        if (isMainAttitudeQuestion) {
          if (readonly) {
            return (
              <div className="flex items-center gap-4">
                <span className="text-base font-medium">{field.label}</span>
                <span className="text-base font-semibold">
                  {fieldValue || 'No answer provided'}
                </span>
              </div>
            );
          }
          return (
            <div
              className="border-t border-b border-gray-300"
              style={{ borderColor: '#DAE1E7' }}
            >
              <div className="flex items-center py-3 px-4 bg-white">
                <span
                  className="font-bold text-sm mr-8"
                  style={{
                    fontSize: '14px',
                    fontWeight: 700,
                    whiteSpace: 'nowrap',
                  }}
                >
                  {field.label}
                </span>
                <div className="flex items-center gap-4">
                  {radioField.options.map((option) => (
                    <label
                      key={option}
                      className="flex items-center gap-2 cursor-pointer"
                    >
                      <input
                        type="radio"
                        name={name}
                        value={option}
                        checked={fieldValue === option}
                        onChange={() => {
                          if (!readonly) {
                            onChange(option);
                          }
                        }}
                        readOnly={readonly}
                        className="w-4 h-4 border-gray-300 focus:ring-black text-black"
                        style={{
                          accentColor: '#000000',
                          color: '#000000',
                          opacity: readonly ? 1 : 1,
                        }}
                      />
                      <span className="text-sm">{option}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          );
        }

        if (isMainExerciseQuestion) {
          return (
            <div className=" border-b border-gray-300 py-4">
              {radioComponent}
            </div>
          );
        } else if (isKnowledgeQuestion) {
          const isEvenRow = fieldIndex % 2 === 0;
          const rowBgColor = isEvenRow ? 'bg-sky-100' : 'bg-white';

          return (
            <div
              className={`grid grid-cols-5 gap-0 border border-gray-300 divide-x divide-gray-300  min-h-[60px] items-start ${rowBgColor}`}
            >
              <div className="p-2 flex items-start ">
                <span className="text-sm text-gray-900 leading-relaxed break-words">
                  {field.label}
                </span>
              </div>

              {[0, 1, 2, 3].map((optionIndex) => {
                const option = radioOptions[optionIndex];
                return (
                  <div
                    key={optionIndex}
                    className=" flex justify-center w-full h-full  "
                    style={{ paddingTop: '12px' }}
                  >
                    {option ? (
                      <label className="flex items-start cursor-pointer  px-2 w-[150px]">
                        <input
                          type="radio"
                          name={name}
                          value={option.value}
                          checked={fieldValue === option.value}
                          onChange={() => onChange(option.value)}
                          className="w-4 h-4 accent-black border-gray-300 focus:ring-black flex-shrink-0"
                          hidden={readonly}
                          style={{
                            marginTop: '0px',
                            accentColor: '#000000',
                            opacity: readonly ? 1 : 1,
                          }}
                        />
                        <span className="ml-2 text-sm text-gray-900 leading-tight break-words">
                          {option.label}
                        </span>
                      </label>
                    ) : (
                      <div className="w-4 h-4"></div>
                    )}
                  </div>
                );
              })}
            </div>
          );
        } else {
          return customStyles.wrapperClassName ? (
            <div className={customStyles.wrapperClassName}>
              {radioComponent}
            </div>
          ) : (
            radioComponent
          );
        }
      }}
    />
  );
};

export const SliderField: React.FC<FieldComponentProps> = ({
  name,
  field,
  control,
  errors,
  readonly,
  sectionId,
}) => {
  const sliderField = field as ISliderField;

  // Check if field has custom styles defined
  const customStyles = (field as any).customStyles || {};

  // Check if this is a physical activity attitude section slider (importance/confidence)
  // We only suppress helperText for physical activity attitude sliders since they have manual labels
  const isPhysicalActivityAttitudeSlider =
    (name.includes('importance') ||
      name.includes('confidence') ||
      field.label?.toLowerCase().includes('important') ||
      field.label?.toLowerCase().includes('confident')) &&
    name.toLowerCase().includes('physical');

  const minValue = 0;
  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange, value } }) => {
        const fieldValue =
          readonly && (field as any).value !== undefined
            ? (field as any).value
            : value;

        return (
          <AppSlider
            value={fieldValue ?? minValue}
            onChange={onChange}
            label={field.label}
            min={minValue}
            max={sliderField.max}
            step={sliderField.step}
            helperText={
              isPhysicalActivityAttitudeSlider ? undefined : field.description
            }
            errors={errors?.[name]}
            required={field.required}
            showIndicator
            disabled={readonly}
            customStyles={customStyles}
            labelVariant={
              sectionId === 'food_intake' && readonly ? 'vertical' : undefined
            }
            labelClassName={
              customStyles.labelMinWidth
                ? `min-w-[${customStyles.labelMinWidth}]`
                : undefined
            }
            sx={{
              ...customStyles.sliderStyles,
            }}
          />
        );
      }}
    />
  );
};

export const TextField: React.FC<FieldComponentProps> = ({
  name,
  field,
  control,
  errors,
  readonly,
  customStyles,
}) => {
  const numberField = field as INumberField | ITextField;
  return (
    <Controller
      name={name}
      control={control}
      render={({ field: formField }) => {
        const fieldValue =
          readonly && (field as any).value !== undefined
            ? (field as any).value
            : formField.value;
        const maxWidthClass = customStyles?.fieldMaxWidth
          ? `max-w-[${customStyles.fieldMaxWidth}]`
          : undefined;
        return (
          <AppTextField
            {...formField}
            value={fieldValue}
            type={numberField.type}
            label={numberField.label}
            placeholder={numberField.label}
            required={numberField.required}
            error={!!errors?.[name]}
            disabled={readonly}
            initiallyReadonly
            className={cn(maxWidthClass)}
          />
        );
      }}
    />
  );
};

export const ConditionalField: React.FC<FieldComponentProps> = ({
  name,
  field,
  control,
  errors,
  watch,
  readonly,
}) => {
  const conditionalField = field as IConditionalField;
  const currentValue = watch?.(name);
  const selectedCondition = conditionalField.conditions.find(
    (cond) => cond.label === currentValue?.value
  );

  const renderAdditionField = useCallback(
    (optionLabel: string) => {
      if (!selectedCondition?.subField) return null;
      const { label, ...subField } = selectedCondition.subField;
      const inputLabel = label;

      if (optionLabel === selectedCondition.label) {
        return (
          <div className="flex gap-base items-center">
            <span className="whitespace-nowrap font-semibold">
              {inputLabel}
            </span>
            <TextField
              name={`${name}.subField`}
              field={subField}
              control={control}
              readonly={readonly}
              customStyles={subField.customStyles}
            />
          </div>
        );
      } else {
        return null;
      }
    },
    [selectedCondition, control, name, readonly]
  );

  if (readonly) {
    if (!currentValue) return null;
    if (currentValue?.value === 'Yes') {
      return (
        <span className="text-base">
          I drink {currentValue?.subField} cups of {field.label}
        </span>
      );
    } else if (currentValue?.value === 'No') {
      return (
        <span className="text-base">No, I don&apos;t drink {field.label}</span>
      );
    }
    return null;
  }

  return (
    <div
      className={cn(
        'space-y-4',
        conditionalField.customStyles?.wrapperClassName
      )}
    >
      <Controller
        name={`${name}.value`}
        control={control}
        render={({ field: { onChange, value } }) => (
          <AppRadio
            name={`${name}.value`}
            value={value}
            onChange={onChange}
            label={field.label}
            options={conditionalField.conditions.map((cond) => ({
              value: cond.label ?? '',
              label: (
                <div className="flex gap-10 items-center">
                  {cond.label}
                  {renderAdditionField(cond?.label ?? '')}
                </div>
              ),
            }))}
            disabled={readonly}
            errors={errors?.[name]}
            required={field.required}
            formControlProps={{
              sx: {
                width: '100%',
                display: 'flex',
                flexDirection: 'row',
                gap: 2,
                ['& .app-input-label']: { minWidth: 200 },
              },
            }}
          />
        )}
      />
    </div>
  );
};

export const TextareaField: React.FC<FieldComponentProps> = ({
  name,
  field,
  control,
  errors: _errors,
  readonly,
}) => {
  const isPhysicalChangeField =
    field.label?.toLowerCase().includes('physical change') ||
    /^physical change \d+$/i.test(field.label || '');

  const isNutritionChangeField =
    field.label?.toLowerCase().includes('nutrition change') ||
    field.label?.toLowerCase().includes('dietary change') ||
    field.label?.toLowerCase().includes('nutritional change') ||
    /^nutrition change \d+$/i.test(field.label || '');

  const isChangeField = isPhysicalChangeField || isNutritionChangeField;
  const minRows = isChangeField ? 1 : 3;

  const placeholder = isChangeField ? '' : field.label;

  return (
    <div className="space-y-2">
      {isChangeField && (
        <label
          className={`block text-sm text-gray-700 ${readonly ? 'font-bold' : 'font-medium'}`}
        >
          {field.label}
        </label>
      )}

      <Controller
        name={name}
        control={control}
        render={({ field: controllerField }) => {
          const fieldValue =
            readonly && (field as any).value !== undefined
              ? (field as any).value
              : controllerField.value;

          if (readonly && isChangeField) {
            return (
              <div className="text-sm text-gray-900 leading-relaxed">
                {fieldValue || 'No content provided'}
              </div>
            );
          }

          return isChangeField ? (
            <div className="relative">
              <TextareaAutosize
                {...controllerField}
                value={fieldValue}
                placeholder={placeholder}
                minRows={minRows}
                disabled={readonly}
                style={{
                  width: '100%',
                  padding: '8px 40px 8px 8px',
                  border: '1px solid #ccc',
                  borderRadius: '4px',
                  fontFamily: 'inherit',
                  fontSize: '14px',
                  resize: 'none',
                }}
              />
              <div className="absolute right-2 top-2 pointer-events-none">
                <PenIcon className="h-4 w-4 text-gray-400" />
              </div>
            </div>
          ) : (
            <TextareaAutosize
              {...controllerField}
              value={fieldValue}
              placeholder={placeholder}
              minRows={minRows}
              disabled={readonly}
              style={{
                width: '100%',
                padding: '8px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                fontFamily: 'inherit',
                fontSize: '14px',
                resize: 'vertical',
              }}
            />
          );
        }}
      />
    </div>
  );
};

export const FieldGroup: React.FC<FieldComponentProps> = ({
  name,
  field,
  control,
  readonly,
}) => {
  const { fields = [] } = field as any;

  if (!fields || fields.length === 0) {
    return null;
  }

  if (readonly) {
    return (
      <Box
        sx={{
          border: '1px solid #E2E8F0',
          borderRadius: '8px',

          backgroundColor: 'white',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            gap: 10,
          }}
        >
          {fields.map((subField: any) => (
            <Box
              key={subField.id}
              sx={{
                borderRight: '1px solid #E2E8F0',
                p: 3,
                // mb: 2,
                display: 'flex',
                alignItems: 'center',
                gap: 2,
                justifyContent: 'center',
              }}
            >
              <Typography variant="body2">{subField.label}</Typography>
              <Controller
                name={`${name}.${subField.id}`}
                control={control}
                render={({ field: controllerField }) => (
                  <Typography variant="body2">
                    {controllerField.value || '-'}
                  </Typography>
                )}
              />
            </Box>
          ))}
        </Box>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        border: '1px solid #E2E8F0',
        borderRadius: '8px',
        p: 2,
        mb: 2,
        backgroundColor: 'white',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          gap: 5,
          justifyContent: 'space-between',
        }}
      >
        {fields.map((subField: any) => (
          <Box
            key={subField.id}
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1.5,
            }}
          >
            <Typography
              variant="body1"
              sx={{
                fontSize: '16px',
                fontWeight: 600,
                color: 'text.primary',
              }}
            >
              {subField.label}
            </Typography>
            <Box sx={{ flex: 1 }}>
              <Controller
                name={`${name}.${subField.id}`}
                control={control}
                render={({ field: controllerField, fieldState: { error } }) => (
                  <AppTextField
                    {...controllerField}
                    type={subField.type === 'number' ? 'text' : subField.type}
                    onKeyDown={
                      subField.type === 'number'
                        ? (e: React.KeyboardEvent<HTMLDivElement>) =>
                            allowNumbersWithDecimals(
                              e as React.KeyboardEvent<HTMLInputElement>
                            )
                        : undefined
                    }
                    inputProps={{
                      inputMode:
                        subField.type === 'number' ? 'decimal' : 'text',
                      pattern:
                        subField.type === 'number'
                          ? '[0-9]*(\\.[0-9]+)?'
                          : undefined,
                      min: subField.min,
                      step: subField.step,
                    }}
                    error={!!error}
                    helperText={error?.message}
                    disabled={readonly}
                    sx={{ maxWidth: 100 }}
                    size="small"
                  />
                )}
              />
            </Box>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export const SectionField: React.FC<FieldComponentProps> = ({
  name,
  field,
  control,
  watch,
  setValue,
  readonly,
}) => {
  const sectionField = field as ISectionField;

  const isReasonSection = field.label
    ?.toLowerCase()
    .includes('reason for your choice');

  const isKnowledgeSection =
    field.label?.toLowerCase().includes('knowledge') ||
    field.label?.toLowerCase().includes('benefit of exercise') ||
    field.label?.toLowerCase().includes('examples of') ||
    field.label?.toLowerCase().includes('training') ||
    field.label?.toLowerCase().includes('exercise') ||
    field.label?.toLowerCase().includes('walking') ||
    field.label?.toLowerCase().includes('material') ||
    field.label?.toLowerCase().includes('food') ||
    field.label?.toLowerCase().includes('minimum') ||
    field.label?.toLowerCase().includes('which') ||
    field.label?.toLowerCase().includes('what is');

  if ((isReasonSection || isKnowledgeSection) && sectionField.fields) {
    return (
      <div className={isKnowledgeSection ? 'space-y-1' : 'space-y-4'}>
        <h4
          className={`font-medium ${isKnowledgeSection ? 'text-sm mb-2' : 'text-base'}`}
        >
          {field.label}
        </h4>
        <div
          className={`${isKnowledgeSection ? 'bg-white border border-gray-50 rounded-lg overflow-hidden ring-1 ring-gray-50 ml-4' : 'rounded-lg overflow-hidden border border-gray-50'}`}
        >
          {sectionField.fields.map((subField, index) => {
            const fieldName = `${name}.fields.${index}.value`;
            const radioField = subField as IRadioField;

            const whiteBackgroundLabels = [
              'Availability of Gyms',
              'Subjective Feeling of Gym Workouts',
              'Social Support for Gym Attendance',
              'Availability of healthy food',
              'Subjective Feeling',
              'Social Support',
            ];
            const isWhiteBackground =
              whiteBackgroundLabels.includes(subField.label || '') ||
              whiteBackgroundLabels.includes(field.label || '');
            const rowBgColor = isWhiteBackground
              ? 'bg-white'
              : readonly
                ? 'bg-[#DAE1E7]'
                : 'bg-sky-100';

            return (
              <div
                key={subField.id}
                className={`${isKnowledgeSection ? 'grid grid-cols-5 gap-0 border-b border-gray-50 last:border-b-0 hover:bg-gray-50 divide-x divide-gray-200' : `grid grid-cols-3 border-b border-gray-50 last:border-b-0 divide-x divide-gray-200 ${rowBgColor}`}`}
                style={
                  isKnowledgeSection
                    ? { display: 'grid', gridTemplateColumns: 'repeat(5, 1fr)' }
                    : undefined
                }
              >
                {isKnowledgeSection ? (
                  <>
                    <div className="p-2 flex items-start">
                      <span className="text-sm text-gray-700 leading-relaxed break-words">
                        {subField.label}
                      </span>
                    </div>

                    {[0, 1, 2, 3].map((optionIndex) => {
                      const option = radioField.options?.[optionIndex];
                      return (
                        <div
                          key={optionIndex}
                          className="p-2 flex items-center justify-center"
                        >
                          {option && (
                            <Controller
                              name={fieldName}
                              control={control}
                              render={({ field: { onChange, value } }) => {
                                const fieldValue =
                                  readonly &&
                                  (subField as any).value !== undefined
                                    ? (subField as any).value
                                    : value;

                                return (
                                  <label className="flex items-center space-x-1.5 cursor-pointer">
                                    <input
                                      type="radio"
                                      value={option}
                                      checked={fieldValue === option}
                                      onChange={() => onChange(option)}
                                      readOnly={readonly}
                                      className={`w-3.5 h-3.5 border-gray-300 focus:ring-black-500 !text-black-600 !accent-black-600 ${readonly ? '!text-black-800 !accent-black-800' : ''}`}
                                      style={{
                                        accentColor: '#000000',
                                      }}
                                    />
                                    <span className="text-sm text-gray-700">
                                      {option}
                                    </span>
                                  </label>
                                );
                              }}
                            />
                          )}
                        </div>
                      );
                    })}
                  </>
                ) : (
                  <>
                    <div
                      className={`p-3 flex items-center border-r border-gray-700 ${rowBgColor}`}
                    >
                      <span className="font-normal text-sm">
                        {subField.label}
                      </span>
                    </div>

                    <div className="col-span-2 grid grid-cols-2">
                      {radioField.options?.map((option, optionIndex) => (
                        <div
                          key={option}
                          className={`p-3 flex items-center ${optionIndex < (radioField.options?.length || 0) - 1 ? 'border-r border-black' : ''}`}
                        >
                          <Controller
                            name={fieldName}
                            control={control}
                            render={({ field: { onChange, value } }) => {
                              const fieldValue =
                                readonly &&
                                (subField as any).value !== undefined
                                  ? (subField as any).value
                                  : value;

                              return (
                                <label className="flex items-center space-x-2 cursor-pointer">
                                  <input
                                    type="radio"
                                    value={option}
                                    checked={fieldValue === option}
                                    onChange={() => onChange(option)}
                                    readOnly={readonly}
                                    className={`w-4 h-4 border-black focus:ring-black !text-black !accent-black ${readonly ? '!text-black !accent-black' : ''}`}
                                    style={{
                                      color: '#000000',
                                      accentColor: '#000000',
                                    }}
                                  />
                                  <span className="text-sm">{option}</span>
                                </label>
                              );
                            }}
                          />
                        </div>
                      ))}
                    </div>
                  </>
                )}
              </div>
            );
          })}
        </div>
      </div>
    );
  }

  const isPhysicalChangesSection = field.label
    ?.toLowerCase()
    .includes('rank the top 3 physical changes');

  const isNutritionAttitudeSection =
    field.label?.toLowerCase().includes('physical change') ||
    field.label?.toLowerCase().includes('nutrition change') ||
    field.label?.toLowerCase().includes('nutritional change') ||
    field.label?.toLowerCase().includes('dietary change') ||
    (sectionField.fields &&
      sectionField.fields.length >= 3 &&
      sectionField.fields[0]?.type === 'textarea' &&
      sectionField.fields[1]?.label?.toLowerCase().includes('important') &&
      sectionField.fields[2]?.label?.toLowerCase().includes('confident'));

  if (
    (isPhysicalChangesSection || isNutritionAttitudeSection) &&
    sectionField.fields
  ) {
    return (
      <div className="space-y-4">
        <h4 className="text-base font-medium">{field.label}</h4>
        <div className="space-y-6">
          {sectionField.fields &&
            Array.from(
              { length: Math.ceil(sectionField.fields.length / 3) },
              (_, groupIndex) => {
                const startIndex = groupIndex * 3;
                const textareaField = sectionField.fields![startIndex];
                const importanceField = sectionField.fields![startIndex + 1];
                const confidenceField = sectionField.fields![startIndex + 2];

                if (!textareaField || !importanceField || !confidenceField)
                  return null;

                const TextareaComponent = fieldComponents[textareaField.type];
                const ImportanceComponent =
                  fieldComponents[importanceField.type];
                const ConfidenceComponent =
                  fieldComponents[confidenceField.type];

                if (
                  !TextareaComponent ||
                  !ImportanceComponent ||
                  !ConfidenceComponent
                ) {
                  return null;
                }

                const isNutritionSection =
                  field.label?.toLowerCase().includes('nutrition') ||
                  field.label?.toLowerCase().includes('dietary');

                const isPhysicalSection = field.label
                  ?.toLowerCase()
                  .includes('physical');

                const numberedLabel = isNutritionSection
                  ? `Nutrition Change ${groupIndex + 1}`
                  : isPhysicalSection
                    ? `Physical Change ${groupIndex + 1}`
                    : textareaField.label;

                return (
                  <div key={`group-${groupIndex}`}>
                    {/* Add grey horizontal line before sections 2 and 3 */}
                    {groupIndex > 0 &&
                      (isNutritionSection || isPhysicalSection) && (
                        <div className="border-t border-gray-300 my-6"></div>
                      )}
                    <div className="space-y-3">
                      <div>
                        <TextareaComponent
                          name={`${name}.fields.${startIndex}.value`}
                          field={{
                            ...textareaField,
                            label: numberedLabel,
                          }}
                          control={control}
                          watch={watch}
                          setValue={setValue}
                          readonly={readonly}
                        />
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="text-sm font-medium text-gray-700">
                          {importanceField.label}
                        </div>
                        <div className="text-sm font-medium text-gray-700">
                          {confidenceField.label}
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <ImportanceComponent
                            name={`${name}.fields.${startIndex + 1}.value`}
                            field={{ ...importanceField, label: '' }}
                            control={control}
                            watch={watch}
                            setValue={setValue}
                            readonly={readonly}
                          />
                          {isPhysicalSection && (
                            <div className="text-xs text-gray-500 mt-1 text-left">
                              (0=not important to 10=very important)
                            </div>
                          )}
                        </div>
                        <div>
                          <ConfidenceComponent
                            name={`${name}.fields.${startIndex + 2}.value`}
                            field={{ ...confidenceField, label: '' }}
                            control={control}
                            watch={watch}
                            setValue={setValue}
                            readonly={readonly}
                          />
                          {isPhysicalSection && (
                            <div className="text-xs text-gray-500 mt-1 text-left">
                              (0=not confident to 10=very confident)
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              }
            )}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h4 className="text-base font-medium ">{field.label}</h4>
      {sectionField.fields && sectionField.fields.length > 0 && (
        <div className="space-y-4">
          {sectionField.fields.map((subField, index) => {
            const FieldComponent = fieldComponents[subField.type];
            if (!FieldComponent) {
              console.warn(
                `No renderer found for field type: ${subField.type}`
              );
              return null;
            }

            const fieldName =
              subField.type === 'table'
                ? `${name}.fields.${index}`
                : `${name}.fields.${index}.value`;

            return (
              <div key={subField.id} className="ml-4">
                <FieldComponent
                  name={fieldName}
                  field={subField}
                  control={control}
                  watch={watch}
                  setValue={setValue}
                  readonly={readonly}
                />
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export const ConditionalSelectField: React.FC<FieldComponentProps> = ({
  name,
  field,
  control,
  errors,
  readonly,
}) => {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange, value } }) => {
        const fieldValue =
          readonly && (field as any).value !== undefined
            ? (field as any).value
            : value;

        return (
          <AppTextField
            value={fieldValue}
            onChange={onChange}
            label={field.label}
            placeholder={field.label}
            required={field.required}
            error={!!errors?.[name]}
            disabled={readonly}
            initiallyReadonly
          />
        );
      }}
    />
  );
};

export const fieldComponents: Record<
  LifestyleFields,
  React.FC<FieldComponentProps>
> = {
  radio: RadioField,
  slider: SliderField,
  number: TextField,
  conditional: ConditionalField,
  text: TextField,
  table: TableField,
  textarea: TextareaField,
  section: SectionField,
  grouped_table: GroupedTableField,
  time_range: TextField,
  conditional_select: ConditionalSelectField,
  frequency: Fragment as React.FC<FieldComponentProps>,
  searchable_select: TextField, // Using TextField as a fallback
  dependent_autofill: TextField, // Using TextField as a fallback
  field_group: FieldGroup,
} as const;
