import React from 'react';

import {
  Accordion as MuiAccordion,
  AccordionSummary,
  AccordionDetails,
  styled,
} from '@mui/material';
import { IoChevronDown } from 'react-icons/io5';

import { cn } from '@/lib/utils';

import ExpandSimpleIcon from '@/assets/svg/ExpandSimpleIcon';
import LockClosedIcon from '@/assets/svg/LockClosedIcon';
import LockOpenIcon from '@/assets/svg/LockOpenIcon';
import PrintIcon from '@/assets/svg/PrintIcon';

import OutlinedIconButton from '../lifestyle/lifestyle-forms/shared/OutlinedIconButton';

const StyledAccordion = styled(MuiAccordion)<{
  width?: string;
  expand?: boolean;
}>(({ expanded, theme, width, expand }) => ({
  width: width || '100%',
  minWidth: '100%',
  backgroundColor: 'transparent',
  boxShadow: 'none',
  '&:before': {
    display: 'none',
  },
  ['& .MuiAccordionSummary-root']: {
    height: 48,
    minHeight: 48,
    padding: '0 12px',
    borderRadius: '6px',
    backgroundColor: expanded ? '#B4E5FE' : '#E6F6FF',

    border: '1px solid #E0E0E0',
    '&.Mui-expanded': {
      minHeight: 48,
      borderBottomLeftRadius: 0,
      borderBottomRightRadius: 0,
    },
  },

  ['& .MuiAccordionSummary-content']: {
    margin: 0,
    height: '100%',
    minHeight: '100%',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: '16px',
    '&.Mui-expanded': {
      margin: 0,
    },
  },

  ['& .MuiAccordionDetails-root']: {
    backgroundColor: 'white',
    padding: 8,
    border: '2px solid #E0E0E0',
    borderTop: 'none',
    borderBottomLeftRadius: '8px',
    borderBottomRightRadius: '8px',
  },
}));

interface AccordionProps {
  children: React.ReactNode;
  designation?: string;
  doctorName?: string;
  date?: string;
  department?: string; // Added department prop
  open: boolean;
  onToggle?: () => void;
  onFinalise?: () => void;
  onExpand?: () => void;
  onPrint?: () => void;
  showFinaliseButton?: boolean;
  isDownloadPDF?: boolean;
  pdfDocument?: JSX.Element;
  fileName?: string;
  expand?: boolean;
  width?: string;
  className?: string;
  hideFinaliseButton?: boolean;
  finalized?: boolean;
}

const Accordion: React.FC<AccordionProps> = ({
  children,
  onToggle,
  open,
  date,
  doctorName,
  designation,
  department, // Added department
  onFinalise,
  onExpand,
  onPrint,
  showFinaliseButton = true,
  finalized = false,
  expand,
  width,
  className = '',
  hideFinaliseButton = false,
}) => {
  return (
    <StyledAccordion
      className={` h-full ${className}`}
      expanded={open}
      onChange={onToggle}
      width={width}
      expand={expand}
    >
      <AccordionSummary className="gap-1">
        <div className="flex items-center gap-3 text-blue-900 w-full">
          {expand ? (
            <OutlinedIconButton
              size="small"
              showBorder={true}
              sx={{
                backgroundColor: 'white !important',
                width: '32px !important',
                height: '32px !important',
                minWidth: '32px !important',
              }}
            >
              <div className="w-8 h-8 rounded-full bg-transparent flex items-center justify-center text-[#6B7280] font-semibold text-sm">
                {doctorName
                  ?.split(' ')
                  .map((n) => n[0])
                  .join('')
                  .toUpperCase()
                  .slice(0, 2) || '--'}
              </div>
            </OutlinedIconButton>
          ) : (
            <OutlinedIconButton
              size="small"
              showBorder={true}
              sx={{
                backgroundColor: 'white !important',
                width: '32px !important',
                height: '32px !important',
                minWidth: '32px !important',
              }}
            >
              <IoChevronDown
                className={cn('transition-transform duration-200 text-base', {
                  'transform -rotate-90': !open,
                })}
              />
            </OutlinedIconButton>
          )}

          {/* Single line layout */}
          <div className="flex items-center w-full">
            {/* Left side - Doctor name and designation in single line */}
            <div className="flex items-center gap-2 flex-shrink-0">
              <span className="font-semibold text-[18px] text-[#001926] leading-none">
                {doctorName}
              </span>
              {designation && (
                <span
                  className="text-[#64707D] text-[14px] font-light leading-none"
                  style={{ fontWeight: 300 }}
                >
                  {designation}
                </span>
              )}
            </div>

            {/* Center - Department and Date */}
            <div className="flex items-center justify-center gap-4 flex-1">
              {department && (
                <span
                  className="text-[#001926] text-[14px] font-light leading-none"
                  style={{ fontWeight: 300 }}
                >
                  {department}
                </span>
              )}
              {date && (
                <span
                  className="text-[#64707D] text-[14px] font-light leading-none"
                  style={{ fontWeight: 300 }}
                >
                  {date}
                </span>
              )}
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {!hideFinaliseButton && (
            <OutlinedIconButton
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                if (!finalized) {
                  onFinalise?.();
                }
              }}
              disabled={finalized || !showFinaliseButton}
              sx={{
                backgroundColor: 'white !important',
                width: '32px !important',
                height: '32px !important',
                minWidth: '32px !important',
              }}
            >
              {finalized ? (
                <LockClosedIcon className="text-sm" />
              ) : (
                <LockOpenIcon className="text-sm" />
              )}
            </OutlinedIconButton>
          )}
          <OutlinedIconButton
            onClick={(e) => {
              e.stopPropagation();
              onPrint?.();
            }}
            showBorder={true}
            sx={{
              backgroundColor: 'white !important',
              width: '32px !important',
              height: '32px !important',
              minWidth: '32px !important',
            }}
          >
            <PrintIcon />
          </OutlinedIconButton>
          <OutlinedIconButton
            onClick={(e) => {
              e.stopPropagation();
              onExpand?.();
            }}
            showBorder={true}
            sx={{
              backgroundColor: 'white !important',
              width: '32px !important',
              height: '32px !important',
              minWidth: '32px !important',
            }}
          >
            <ExpandSimpleIcon className="text-sm" />
          </OutlinedIconButton>
        </div>
      </AccordionSummary>
      <AccordionDetails className="accordion-details">
        {children}
      </AccordionDetails>
    </StyledAccordion>
  );
};

export default Accordion;
