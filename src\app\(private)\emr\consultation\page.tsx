'use client';

import { useCallback, useEffect, useMemo, useState } from 'react';

import { Box, IconButton, Tooltip } from '@mui/material';
import { maxBy } from 'lodash';
import { toast } from 'sonner';

import { useSearchParams } from 'next/navigation';

import { DateRange } from '@core/components/date-range-picker/types';

import AddRecord from '@/lib/add_record';
import EditableText from '@/lib/common/editable_text';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import {
  EmrTypes,
  useCustomiseEmrStore,
} from '@/store/emr/doctor-profile/customise-emr';
import { useDoctorStore } from '@/store/emr/doctor-profile/personal-info';
import { useDiagnosisStore } from '@/store/extraNoteDiagnosisStore';
import { useUserStore } from '@/store/userStore';

import {
  addToPatientHistory,
  getPatientHistory,
  GetPatientHistoryRes,
  getPatientVitals,
  uploadVitals,
  UploadVitalsParams,
} from '@/query/patient';
import { SummarizeConversationRes } from '@/query/speech';

import { pageIds } from '@/utils/constants/docAssist';
import { DateFormats } from '@/utils/dateUtils/dateFormats';
import { formatDate } from '@/utils/dateUtils/dayUtils';
import { isHtmlContentEmpty } from '@/utils/textUtil';

import ExpandNoteIcon from '@/assets/svg/ExpandNoteIcon';

import { calculateAge } from '@/helpers/dates';

import { ConsultationPage } from '@/components/permission/protected-page';

import Chat from '@/emr/components/chat';
import {
  groupByFormattedDate,
  StyledTypography,
  useGroupedRecords,
} from '@/emr/components/consultation/Common';
import ConsultationTimeline from '@/emr/components/consultation/ConsultationTimeline';
import DiagnosisCard from '@/emr/components/consultation/DiagnosisCard';
import DiagnosisNoteModal from '@/emr/components/consultation/ExtraNoteModal';
import SummaryModal, {
  SummaryModalMode,
} from '@/emr/components/consultation/summary-modal';
import VitalsTab from '@/emr/components/consultation/vitals-tab';
import DocAssist from '@/emr/components/doc_assist';
import PatientCard from '@/emr/components/patient-card';
import NoPatientView from '@/emr/components/shared/NoPatientView';

import '../lifestyle-dep/styles.scss';

enum PageState {
  Base = 'Base',
  AddSummaryManually = 'AddSummaryManually',
  PreviewSummary = 'PreviewSummary',
}

const { STANDARD_DATE } = DateFormats;

const ConsultationContent = () => {
  const { patient, setPatient, isActivePatient } = useCurrentPatientStore();
  const { data: userData } = useUserStore();
  const { doctorProfile } = useDoctorStore();
  const { getExtraNotes, records } = useDiagnosisStore();
  const { customiseEmrData, fetchCustomiseEmr } = useCustomiseEmrStore();
  const searchParams = useSearchParams();
  const isFromPatientSearch = searchParams.get('source') === 'patient_search';

  const [open, setOpen] = useState(false);
  const [selectedTitle, setSelectedTitle] = useState<string | null>(null);
  const [showCancelButton, setShowCancelButton] = useState(true);
  const [currentHistory, setCurrentHistory] = useState<
    GetPatientHistoryRes[0] | null
  >(null);
  const [patientHistory, setPatientHistory] = useState<GetPatientHistoryRes>(
    []
  );
  const [state, setState] = useState(PageState.Base);
  const [isFetching, setIsFetching] = useState(false);
  const [isDataLoading, setIsDataLoading] = useState(false);
  const [isSavingFromAddRecord, setIsSavingFromAddRecord] = useState(false);
  const [consultationDateRange, setConsultationDateRange] = useState<DateRange>(
    {
      from: undefined,
      to: undefined,
    }
  );

  const diagnosisRecords = useGroupedRecords({ records, title: 'Diagnosis' });

  const vitals = useMemo(() => {
    if (!patient?.vitals) {
      return [];
    }

    return patient.vitals.map((vital) => {
      return {
        ...vital.vitals,
        height: Number(vital.vitals.height),
        weight: Number(vital.vitals.weight),
        bmi: Number(vital.vitals.bmi),
        updatedOn: vital.updated_on,
      } as any;
    });
  }, [patient]);

  const lastCreatedEmrData = maxBy<EmrTypes>(
    customiseEmrData,
    (item) => new Date(item.created_on as string)
  );

  const sections = Object.keys(lastCreatedEmrData || {})
    .filter((key) => key.startsWith('extraNote') && lastCreatedEmrData?.[key])
    .map((key) => ({
      title: lastCreatedEmrData?.[key],
      placeholder: `Enter ${lastCreatedEmrData?.[key].toLowerCase()}`,
    }));

  const fetchPatientVitals = useCallback(async () => {
    if (!patient?.id) {
      return;
    }
    try {
      const vitals = await getPatientVitals(patient.id);
      setPatient({ ...patient, vitals: vitals.data?.reverse() });
    } catch (err) {
      console.error(err);
      toast.error('Error fetching patient data');
    }
  }, [patient, setPatient]);

  const fetchPatientHistory = useCallback(async () => {
    if (!patient?.id) {
      return;
    }
    try {
      setIsFetching(true);
      const history = await getPatientHistory({
        patientId: patient.id,
        startDate: consultationDateRange.from
          ? formatDate(consultationDateRange?.from as string, STANDARD_DATE)
          : undefined,
        endDate: consultationDateRange.to
          ? formatDate(consultationDateRange?.to as string, STANDARD_DATE)
          : undefined,
      });

      setPatientHistory(history || []);
      setTimeout(() => setIsFetching(false), 1000);
    } catch (err) {
      setIsFetching(false);

      console.error(err);
      toast.error('Error fetching patient data');
    }
  }, [patient?.id, consultationDateRange.from, consultationDateRange.to]);

  const startManualRecordFlow = () => {
    setCurrentHistory(null);
    setState(PageState.AddSummaryManually);
  };

  const handleCloseSummaryModal = () => {
    setState(PageState.Base);
    setCurrentHistory(null);
  };

  const handleAddConsultation = async (
    data: SummarizeConversationRes['summary']
  ) => {
    const isFromAddRecord =
      !isDataLoading && state !== PageState.AddSummaryManually;
    if (isFromAddRecord) {
      setIsSavingFromAddRecord(true);
    }
    setIsDataLoading(true);
    try {
      if (patient) {
        setState(PageState.Base);

        await addToPatientHistory(patient.id, {
          owner: {
            id: userData.id,
            role: doctorProfile?.general?.designation || userData.userRole,
            name: doctorProfile?.general?.fullName || userData.name,
            email: userData.email,
            department: doctorProfile?.general?.department,
          },
          ...data,
        });

        const vitals: UploadVitalsParams = {
          heartRate: data.vitals?.heartRate || 0,
          systolicPressure: data.vitals?.systolicPressure || 0,
          diastolicPressure: data.vitals?.diastolicPressure || 0,
          respiratoryRate: data.vitals?.respiratoryRate || 0,
          spO2: data.vitals?.spO2 || 0,
          temperature: data.vitals?.temperature || 0,
          height: data.anthropometry?.height || 0,
          weight: data.anthropometry?.weight || 0,
          bmi: data.anthropometry?.bmi || 0,
          waistCircumference: data.anthropometry?.waistCircumference || 0,
        };

        const isVitalsValid = Object.values(vitals).reduce(
          (acc, curr) => acc || !!curr,
          false
        );

        if (isVitalsValid) {
          await uploadVitals(patient.id, vitals);
        }

        await fetchPatientHistory();
        await fetchPatientVitals();
        setIsDataLoading(false);
        setIsSavingFromAddRecord(false);
        toast.success('Consultation added');
      } else {
        toast.error('Patient not selected');
      }
    } catch {}
  };

  const handleSelectDateRange = useCallback((range: DateRange) => {
    setConsultationDateRange(range);
  }, []);

  useEffect(() => {
    if (doctorProfile?.id) {
      fetchCustomiseEmr(doctorProfile?.id as string);
    }
  }, [doctorProfile?.id, fetchCustomiseEmr]);

  useEffect(() => {
    if (patient?.id) {
      getExtraNotes(patient.id);
    }
  }, [patient?.id, getExtraNotes]);

  useEffect(() => {
    fetchPatientHistory();
  }, [fetchPatientHistory]);

  if (!isFromPatientSearch && (!patient || !isActivePatient)) {
    return <NoPatientView />;
  }

  return (
    <div className="h-full flex flex-col w-full">
      <div className="flex flex-1 h-full rounded-xl gap-base w-full">
        {/* Left Panel */}
        <div className="w-[22%] flex flex-col relative max-w-[24%] min-w-0">
          <div className="flex flex-col bg-white rounded-base shadow-base overflow-hidden h-full">
            <div className="flex flex-col h-full">
              <div className="flex-1 flex flex-col overflow-hidden">
                <div className="flex flex-col h-full overflow-y-auto">
                  <div className="flex flex-col bg-white rounded-lg">
                    <div className="rounded-lg bg-white justify-evenly flex flex-col gap-1 p-2 shadow-custom-xs border border-[#DAE1E7] mb-2">
                      <div className="flex flex-col">
                        <span className="font-medium pb-1 text-lg border-b border-[#C2CDD6] -tracking-[2.2%] px-[5px] text-[#001926]">
                          Now Consulting
                        </span>
                      </div>
                      <PatientCard
                        name={patient?.name}
                        dob={patient?.dob}
                        sex={patient?.sex}
                        address={patient?.address}
                        calculateAge={calculateAge}
                      />
                    </div>
                    {vitals.length > 0 || lastCreatedEmrData?.selected_tiles ? (
                      <div className="mb-2 px-2">
                        <VitalsTab />
                      </div>
                    ) : null}
                  </div>

                  <div className="flex-1 overflow-y-auto p-2">
                    {sections?.map(({ title, placeholder }) => {
                      const matchingRecords = records?.records
                        ?.filter(
                          (record) =>
                            record?.field?.toLowerCase() ===
                              title.toLowerCase() &&
                            !isHtmlContentEmpty(record.content)
                        )
                        .sort((recordA, recordB) => {
                          const dateA = new Date(recordA.timestamp)
                            .toISOString()
                            .split('T')[0];
                          const dateB = new Date(recordB.timestamp)
                            .toISOString()
                            .split('T')[0];
                          return dateA.localeCompare(dateB);
                        });

                      const groupedRecords = groupByFormattedDate(
                        matchingRecords ?? []
                      );

                      return (
                        <div
                          key={title}
                          className="border border-[#DAE1E7] rounded-lg shadow-custom-xs p-1.5 xl:p-2.5 flex flex-col gap-1 w-full"
                        >
                          <div className="flex items-center justify-between">
                            <span className="font-medium text-sm xl:text-base -tracking-[2.2%]">
                              {title}
                            </span>
                            <IconButton
                              onClick={
                                isFromPatientSearch
                                  ? undefined
                                  : () => {
                                      setSelectedTitle(title);
                                      setOpen(true);
                                      setShowCancelButton(false);
                                    }
                              }
                              size="small"
                              disabled={isFromPatientSearch}
                              sx={{
                                '&.Mui-disabled': {
                                  color: 'gray',
                                  opacity: 0.5,
                                },
                                '&:hover': {
                                  backgroundColor: 'transparent',
                                },
                              }}
                            >
                              <ExpandNoteIcon />
                            </IconButton>
                          </div>

                          {Object.keys(groupedRecords).length > 0 ? (
                            Object.keys(groupedRecords).map((date) => (
                              <div
                                key={date}
                                className="flex flex-row items-start w-full"
                              >
                                <div className="flex-grow max-w-[calc(100%-100px)]">
                                  {groupedRecords[date]?.map((record) => (
                                    <Box
                                      key={`${record.record_id}-${record.content}`}
                                    >
                                      <EditableText
                                        defaultValue={record.content}
                                        editable={false}
                                        emptyPlaceholder=""
                                      />
                                    </Box>
                                  ))}
                                </div>
                                <div className="ml-2 w-[100px] text-right">
                                  <StyledTypography variant="caption">
                                    {date}
                                  </StyledTypography>
                                </div>
                              </div>
                            ))
                          ) : (
                            <EditableText
                              bg="white"
                              className="h-[40px]"
                              placeholder={placeholder}
                              editable={!isFromPatientSearch}
                              emptyPlaceholder=""
                            />
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>

              <div className="bg-white py-2 px-5 grid gap-2 border-t border-gray-200">
                <span className="text-[18px] font-bold text-[#012436]">
                  Add record
                </span>
                <Tooltip
                  title={
                    isFromPatientSearch
                      ? 'This feature is disabled in view mode'
                      : ''
                  }
                  arrow
                >
                  <div>
                    <button
                      className={`text-md py-1.5 flex w-full items-center justify-center gap-1.5 rounded-md ${
                        isFromPatientSearch
                          ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                          : 'bg-black text-white hover:bg-gray-800'
                      }`}
                      onClick={
                        !isFromPatientSearch ? startManualRecordFlow : undefined
                      }
                      disabled={isFromPatientSearch}
                    >
                      Manually
                    </button>
                  </div>
                </Tooltip>
                <AddRecord
                  onSave={handleAddConsultation}
                  disabled={isFromPatientSearch}
                  isLoading={isSavingFromAddRecord}
                />
              </div>

              {/* Modals */}
              <DiagnosisNoteModal
                open={open}
                onClose={() => {
                  setOpen(false);
                }}
                selectedTitle={selectedTitle}
                setOpen={setOpen}
                showCancelButton={showCancelButton}
                setShowCancelButton={setShowCancelButton}
              />
              <SummaryModal
                isOpen={
                  (state === PageState.AddSummaryManually || isDataLoading) &&
                  !isSavingFromAddRecord
                }
                data={currentHistory as any}
                mode={
                  state === PageState.AddSummaryManually ||
                  (isDataLoading && !isSavingFromAddRecord)
                    ? SummaryModalMode.Manual
                    : SummaryModalMode.Generated
                }
                onClose={handleCloseSummaryModal}
                onSave={handleAddConsultation}
                loading={isDataLoading}
              />
            </div>
          </div>
        </div>

        <div className="w-[60%] flex flex-col h-full">
          <div className="flex bg-white  rounded-base  flex-col  flex-1 h-full">
            <DiagnosisCard
              diagnosisRecords={diagnosisRecords}
              onExpand={
                isFromPatientSearch
                  ? undefined
                  : () => {
                      setSelectedTitle('Diagnosis');
                      setOpen(true);
                      setShowCancelButton(false);
                    }
              }
              disableExpand={isFromPatientSearch}
            />

            <div className=" bg-white px-2 rounded-base shadow-base flex-1 overflow-hidden h-full">
              <ConsultationTimeline
                patientHistory={patientHistory}
                isFetching={isFetching}
                fetchPatientHistory={async () => await fetchPatientHistory()}
                dateRange={consultationDateRange}
                onSelectDateRange={handleSelectDateRange}
              />
            </div>
          </div>
        </div>

        <div className="w-[18%] flex flex-col overflow-hidden bg-white rounded-base shadow-base">
          <div className="flex-1 overflow-y-auto p-2">
            <DocAssist pageId={pageIds.CONSULTATION} />
          </div>
        </div>
      </div>
      <Chat />
    </div>
  );
};

const Consultation = () => (
  <ConsultationPage className="h-full flex flex-col w-full">
    <ConsultationContent />
  </ConsultationPage>
);

export default Consultation;
