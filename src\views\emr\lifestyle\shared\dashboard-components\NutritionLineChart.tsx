import React from 'react';

import {
  <PERSON><PERSON>hart,
  Line,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  Legend,
  Tooltip,
} from 'recharts';

import { ChartDataPoint } from '@/query/emr/lifestyle/nutrition-dashboard';

import { ChartTitle, LegendItem } from './ChartTitle';

interface NutritionLineChartProps {
  data: ChartDataPoint[];
  dataKey: string;
  color: string;
  title: string;
  unit?: string;
  height?: number;
  showLegend?: boolean;
  yAxisDomain?: [number, number];
}

const NutritionLineChart: React.FC<NutritionLineChartProps> = ({
  data,
  dataKey,
  color,
  title,
  unit = '',
  height = 286,
  showLegend = false,
}) => {
  // Calculate number of days in the data
  const daysCount = data.length;

  // Calculate max value for Y-axis domain
  const calculateMaxYValue = () => {
    if (!data.length) return 100;
    const max = Math.max(...data.map((item) => Number(item[dataKey]) || 0));
    // Round up to nearest 100 for better Y-axis ticks
    return Math.ceil(max / 100) * 100 || 100;
  };

  // Calculate Y-axis ticks based on max value
  const yTicks = () => {
    const yDomainMax = calculateMaxYValue();
    const ticks = [];
    const step = yDomainMax / 5;
    for (let i = 0; i <= 5; i++) {
      ticks.push(Math.round(step * i));
    }
    return ticks;
  };

  const yDomainMax = calculateMaxYValue();
  // Custom tick component for X-axis
  const CustomizedAxisTick = (props: any) => {
    const { x, y, payload } = props;
    const date = new Date(payload.value);
    const day = date.getDate();
    const month = date.toLocaleString('en-US', { month: 'short' });

    return (
      <g transform={`translate(${x},${y})`}>
        <text
          x={0}
          y={0}
          dy={12}
          textAnchor="middle"
          fill="#6b7280"
          fontSize={11}
        >
          {day}
        </text>
        <text
          x={0}
          y={12}
          dy={12}
          textAnchor="middle"
          fill="#6b7280"
          fontSize={9}
        >
          {month}
        </text>
      </g>
    );
  };

  const formatTooltipLabel = (label: string) => {
    const date = new Date(label);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="text-sm font-medium text-gray-900">
            {formatTooltipLabel(label)}
          </p>
          <p className="text-sm text-gray-600">
            <span className="font-medium" style={{ color }}>
              {title}:
            </span>{' '}
            {payload[0].value.toFixed(1)} {unit}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="w-full flex flex-col" style={{ height: `${height}px` }}>
      <ChartTitle
        title={`${title}${unit ? ` (${unit})` : ''}`}
        className="px-4 pt-3 pb-3"
        rightContent={
          showLegend && (
            <div className="flex items-center gap-2">
              <LegendItem color={color} label={title} />
            </div>
          )
        }
      />
      <div className="flex-1">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={data}
            margin={{
              top: 40,
              right: 25,
              left: 25,
              bottom: 2,
            }}
          >
            <defs>
              <linearGradient id="areaFill" x1="0" y1="0" x2="0" y2="1">
                <stop offset="0%" stopColor={color} stopOpacity={0.8} />
                <stop offset="100%" stopColor={color} stopOpacity={0.2} />
              </linearGradient>
              <style>{`
                .recharts-cartesian-grid-horizontal line {
                  stroke-dasharray: 3 3;
                  stroke: #e5e7eb;
                }
                .recharts-cartesian-grid-vertical line {
                  stroke-dasharray: 3 3;
                  stroke: #e5e7eb;
                }
                .recharts-layer {
                  shape-rendering: geometricPrecision;
                }
              `}</style>
            </defs>
            <CartesianGrid
              strokeDasharray="3 3"
              stroke="#e5e7eb"
              horizontal={true}
              vertical={true}
            />
            <XAxis
              dataKey="date"
              axisLine={false}
              tickLine={false}
              interval={0}
              minTickGap={0}
              height={50}
              tick={<CustomizedAxisTick />}
              tickMargin={10}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tick={{ fill: '#6b7280', fontSize: 11 }}
              tickFormatter={(value) => value.toLocaleString()}
              ticks={yTicks()}
              domain={[0, yDomainMax]}
              width={40}
            />
            <Legend wrapperStyle={{ display: 'none' }} />
            <Area
              type="monotone"
              dataKey={dataKey}
              fill="url(#areaFill)"
              stroke="none"
              activeDot={false}
              isAnimationActive={false}
              connectNulls={true}
              baseLine={0}
              style={{
                pointerEvents: 'none',
                zIndex: 5,
                shapeRendering: 'geometricPrecision',
                fillRule: 'evenodd',
                clipRule: 'evenodd',
              }}
            />
            <Line
              type="linear"
              dataKey={dataKey}
              stroke={color}
              strokeWidth={2}
              dot={{ fill: color, strokeWidth: 0.5, r: 2 }}
              activeDot={{ r: 3, stroke: color, strokeWidth: 0.5 }}
              name={title}
              isAnimationActive={false}
              connectNulls={true}
            />
            <Tooltip
              content={({ active, payload, label }) => {
                if (active && payload && payload.length && label) {
                  const date = new Date(label);
                  if (isNaN(date.getTime())) return null;

                  return (
                    <div className="bg-white p-2 border border-gray-200 rounded shadow-md">
                      <p className="text-sm font-medium">
                        {date.toLocaleDateString('en-US', {
                          month: 'short',
                          day: 'numeric',
                        })}
                      </p>
                      <p className="text-sm">
                        {title}: {payload[0].value?.toLocaleString() ?? 'N/A'}{' '}
                        {unit}
                      </p>
                    </div>
                  );
                }
                return null;
              }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default NutritionLineChart;
