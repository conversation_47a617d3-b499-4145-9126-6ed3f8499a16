import React, { useMemo } from 'react';

import { getOrderHistoryTableRows } from '@/utils/emr/lab/order-history';

import { orderHistoryHeaders } from '@/constants/emr/lab/order-history';

import Table from '@/core/components/table';
import { Row, SortOrder } from '@/core/components/table/types';
import { OrderHistoryItem } from '@/types/emr/lab';

interface OrderHistoryTableProps {
  items: OrderHistoryItem[];
  onSort: (field: string, order: SortOrder | null) => void;
  sortField: string;
  sortOrder: SortOrder;
  selectedItems: string[];
  onSelectItem: (id: string) => void;
  onSelectAll: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onSelectGroup: (groupId: string, testIds: string[]) => void;
}

const getSortedItems = (
  items: OrderHistoryItem[],
  sortField: string,
  sortOrder: SortOrder
): OrderHistoryItem[] => {
  if (sortField === 'department' || sortField === 'testName') {
    return [...items].sort((a, b) => {
      const aValue = (a[sortField] || '').toString().toLowerCase();
      const bValue = (b[sortField] || '').toString().toLowerCase();
      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });
  }
  return items;
};

const OrderHistoryTable: React.FC<OrderHistoryTableProps> = ({
  items,
  onSort,
  sortField,
  sortOrder,
  selectedItems,
  onSelectItem,
  onSelectAll,
  onSelectGroup,
}) => {
  const sortedItems = useMemo(
    () => getSortedItems(items, sortField, sortOrder),
    [items, sortField, sortOrder]
  );

  const rows = useMemo<Row[]>(
    () =>
      getOrderHistoryTableRows({
        orders: sortedItems,
        selectedItems,
        onSelectItem,
        onSelectGroup,
      }),
    [sortedItems, selectedItems, onSelectItem, onSelectGroup]
  );

  const headersWithSelectAll = useMemo(() => {
    const isAllSelected =
      items.length > 0 && selectedItems.length === items.length;
    const isIndeterminate =
      selectedItems.length > 0 && selectedItems.length < items.length;

    return orderHistoryHeaders.map((header) => {
      if (header.key === 'checkbox') {
        return {
          ...header,
          header: (
            <input
              type="checkbox"
              className="custom-checkboxhead"
              checked={isAllSelected}
              ref={(el) => {
                if (el) el.indeterminate = isIndeterminate;
              }}
              onChange={onSelectAll}
            />
          ),
        };
      }
      return header;
    });
  }, [items.length, selectedItems.length, onSelectAll]);

  return (
    <Table
      headers={headersWithSelectAll}
      rows={rows}
      sortOptions={{ fieldName: sortField, order: sortOrder, onSort }}
      loading={false}
      stickyHeader
      noDataMessage="No order history found"
      tableContainerProps={{
        sx: {
          maxHeight: '100%',
          '& thead': {
            backgroundColor: '#64707D',
          },
          '& th': {
            whiteSpace: 'nowrap',
            fontSize: '0.875rem',
            padding: '8px 16px',
            borderLeft: '1px solid #EAEDEF',
            backgroundColor: '#64707D',
          },

          '& td': {
            fontSize: '0.725rem',
            padding: '2px 4px',
            border: '1px solid #EAEDEF !important',
            borderTop: 'none !important',
            borderLeft: '1px solid #EAEDEF !important',
            borderRight: '1px solid #EAEDEF !important',
            borderBottom: '1px solid #EAEDEF !important',
          },
          '& tbody td': {
            border: '1px solid #EAEDEF !important',
            borderTop: 'none !important',
            borderLeft: '1px solid #EAEDEF !important',
            borderRight: '1px solid #EAEDEF !important',
            borderBottom: '1px solid #EAEDEF !important',
          },
          '& tbody tr td:first-of-type': {
            borderLeft: '1px solid #EAEDEF !important',
          },
          '& tbody tr td:last-of-type': {
            borderRight: '1px solid #EAEDEF !important',
          },

          '& .sort-icon': {
            color: '#EAEDEF !important',
          },
          '& .sort-icon.selected': {
            color: '#FFFFFF !important',
          },
        },
      }}
    />
  );
};

export default OrderHistoryTable;
