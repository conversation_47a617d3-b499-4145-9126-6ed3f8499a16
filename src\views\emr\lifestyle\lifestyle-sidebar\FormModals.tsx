import { memo } from 'react';

import { lifestyleStore } from '@/store/emr/lifestyle/lifestyle-store';
import { nutritionKnowledgeStore } from '@/store/emr/lifestyle/nutrition/knowledge/knowledge-store';
import { attitudeStore } from '@/store/emr/lifestyle/physical-activity/attitude/attitude-store';
import { knowledgeStore } from '@/store/emr/lifestyle/physical-activity/knowledge/knowledge-store';
import { exercisePatternStore } from '@/store/emr/lifestyle/physical-activity/practice/exercise-pattern-store';

import { LifestyleMode } from '@/constants/emr/lifestyle';
import { LifestyleSources } from '@/constants/emr/lifestyle/lifestyle-sources';

import AppButton from '@/core/components/app-button';
import AppModal from '@/core/components/app-modal';

import NutritionAttitudeModal from '../nutrition/attitude/attitude-tab/AttitudeModal';
import NutritionKnowledgeModal from '../nutrition/knowledge/knowledge-tab/NutritionKnowledgeModal';
import DietaryRecallModal from '../nutrition/practice/24-hour-dietary-recall-tab/DietaryRecallModal';
import FoodFrequencyQuestionnaireModal from '../nutrition/practice/food-frequency-questionnaire/FoodFrequencyQuestionnaireModal';
import FoodIntakePatternModal from '../nutrition/practice/food-intake-patterns-tab/FoodIntakePatternModal';
import AttitudeModal from '../physical-activity/attitude/attitude-tab/AttitudeModal';
import KnowledgeModal from '../physical-activity/knowledge/knowledge-tab/KnowledgeModal';
import ExercisePatternModal from '../physical-activity/practice/exercise-patterns-tab/ExercisePatternModal';

const {
  NUTRITION_PRACTICE_FOOD_INTAKE_PATTERNS,
  NUTRITION_ATTITUDE,
  NUTRITION_KNOWLEDGE,
  PHYSICAL_ACTIVITY_PRACTICE_EXERCISE_PATTERNS,
  PHYSICAL_ACTIVITY_ATTITUDE,
  PHYSICAL_ACTIVITY_KNOWLEDGE,
  NUTRITION_PRACTICE_DIETARY_RECALL,
  NUTRITION_PRACTICE_FOOD_FREQUENCY_QUESTIONNAIRE,
} = LifestyleSources;

const getFormModal = (source: LifestyleSources | null) => {
  switch (source) {
    case NUTRITION_PRACTICE_FOOD_INTAKE_PATTERNS:
      return <FoodIntakePatternModal mode={LifestyleMode.CREATE} />;
    case NUTRITION_ATTITUDE:
      return <NutritionAttitudeModal mode={LifestyleMode.CREATE} />;
    case NUTRITION_KNOWLEDGE:
      return <NutritionKnowledgeModal mode={LifestyleMode.CREATE} />;
    case PHYSICAL_ACTIVITY_PRACTICE_EXERCISE_PATTERNS:
      return <ExercisePatternModal />;
    case PHYSICAL_ACTIVITY_ATTITUDE:
      return <AttitudeModal mode={LifestyleMode.CREATE} />;
    case NUTRITION_PRACTICE_DIETARY_RECALL:
      return <DietaryRecallModal mode={LifestyleMode.CREATE} />;
    case PHYSICAL_ACTIVITY_KNOWLEDGE:
      return <KnowledgeModal mode={LifestyleMode.CREATE} />;
    case NUTRITION_PRACTICE_FOOD_FREQUENCY_QUESTIONNAIRE:
      return <FoodFrequencyQuestionnaireModal mode={LifestyleMode.CREATE} />;
    default:
      return <></>;
  }
};

const getFormTitle = (source: LifestyleSources | null) => {
  switch (source) {
    case NUTRITION_PRACTICE_FOOD_INTAKE_PATTERNS:
      return 'Practice';
    case NUTRITION_ATTITUDE:
      return 'Attitude';
    case NUTRITION_KNOWLEDGE:
      return 'Knowledge';
    case PHYSICAL_ACTIVITY_PRACTICE_EXERCISE_PATTERNS:
      return 'Practice';
    case PHYSICAL_ACTIVITY_ATTITUDE:
      return 'Attitude';
    case PHYSICAL_ACTIVITY_KNOWLEDGE:
      return 'Knowledge';
    case NUTRITION_PRACTICE_DIETARY_RECALL:
      return 'Practice';
    case NUTRITION_PRACTICE_FOOD_FREQUENCY_QUESTIONNAIRE:
      return 'Practice';
    default:
      return 'Add Record';
  }
};

const FormModals = () => {
  const { source, modalOpen, setModalOpen } = lifestyleStore();
  const { patientData: exerciseData } = exercisePatternStore();
  const { patientData: nutritionKnowledgeData } = nutritionKnowledgeStore();
  const { patientData: attitudeData } = attitudeStore();
  const { patientData: knowledgeData } = knowledgeStore();

  const checkTodayEntryExists = () => {
    if (
      source !== PHYSICAL_ACTIVITY_PRACTICE_EXERCISE_PATTERNS &&
      source !== NUTRITION_KNOWLEDGE &&
      source !== PHYSICAL_ACTIVITY_ATTITUDE &&
      source !== PHYSICAL_ACTIVITY_KNOWLEDGE
    ) {
      return false;
    }

    const today = new Date().toISOString().split('T')[0];

    if (source === PHYSICAL_ACTIVITY_PRACTICE_EXERCISE_PATTERNS) {
      return exerciseData.some((entry) => {
        if (!entry.created_on) return false;
        const entryDate = new Date(entry.created_on)
          .toISOString()
          .split('T')[0];
        return entryDate === today;
      });
    }

    if (source === PHYSICAL_ACTIVITY_ATTITUDE) {
      return attitudeData.some((entry) => {
        if (!entry.created_on) return false;
        const entryDate = new Date(entry.created_on)
          .toISOString()
          .split('T')[0];
        return entryDate === today;
      });
    }

    if (source === PHYSICAL_ACTIVITY_KNOWLEDGE) {
      return knowledgeData.some((entry) => {
        if (!entry.created_on) return false;
        const entryDate = new Date(entry.created_on)
          .toISOString()
          .split('T')[0];
        return entryDate === today;
      });
    }

    if (source === NUTRITION_KNOWLEDGE) {
      return nutritionKnowledgeData.some((entry) => {
        if (!entry.created_on) return false;
        const entryDate = new Date(entry.created_on)
          .toISOString()
          .split('T')[0];
        return entryDate === today;
      });
    }
  };

  const isTodayEntryExists = checkTodayEntryExists();
  const isButtonDisabled = !source || isTodayEntryExists;

  return (
    <>
      <AppButton
        onClick={() => setModalOpen(true)}
        fullWidth
        disabled={isButtonDisabled}
        style={{
          backgroundColor: isButtonDisabled ? '#9ca3af' : undefined,
          color: isButtonDisabled ? '#ffffff' : undefined,
        }}
        sx={{
          '&.Mui-disabled': {
            backgroundColor: '#9ca3af !important',
            color: '#ffffff !important',
            '&:hover': {
              backgroundColor: '#9ca3af !important',
            },
          },
          '&[disabled]': {
            backgroundColor: '#9ca3af !important',
            color: '#ffffff !important',
          },
          ...(isButtonDisabled && {
            backgroundColor: '#9ca3af !important',
            color: '#ffffff !important',
          }),
        }}
      >
        Manually
      </AppButton>
      <AppModal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        title={getFormTitle(source)}
        classes={{
          root: 'w-[70vw] h-[80vh] flex flex-col min-h-0',
          body: 'flex-1 h-full flex flex-col overflow-hidden min-h-0',
        }}
      >
        {getFormModal(source)}
      </AppModal>
    </>
  );
};

export default memo(FormModals);
