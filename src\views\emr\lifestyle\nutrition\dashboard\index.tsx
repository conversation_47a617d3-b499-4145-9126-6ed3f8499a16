import { memo, useEffect, useState } from 'react';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useLifestyleFilterStore } from '@/store/emr/lifestyle/filter-store';
import { lifestyleStore } from '@/store/emr/lifestyle/lifestyle-store';
import { useNutritionDashboardStore } from '@/store/emr/lifestyle/nutrition-dashboard-store';

import {
  FilterType,
  getNutritionAverage,
  getNutritionChart,
  getNutritionPercentageChart,
  getNutritionSummary,
} from '@/query/emr/lifestyle/nutrition-dashboard';

// @ts-ignore - We know these are valid image imports
import CaloryIcon from '@/assets/svg/nutrition/Calory.png';
import CarbohydrateIcon from '@/assets/svg/nutrition/Carbohydrate.png';
import FatIcon from '@/assets/svg/nutrition/Fat.png';
import FiberIcon from '@/assets/svg/nutrition/Fibre.png';
import OilIcon from '@/assets/svg/nutrition/Oil.png';
import ProteinIcon from '@/assets/svg/nutrition/Protein.png';
import SaltIcon from '@/assets/svg/nutrition/Salt.png';
import SugarIcon from '@/assets/svg/nutrition/Sugar.png';

import AccordionTable from '@/views/emr/lifestyle/shared/dashboard-components/AccordionTable';
import AverageCard from '@/views/emr/lifestyle/shared/dashboard-components/AverageCard';
import ChartContainer from '@/views/emr/lifestyle/shared/dashboard-components/ChartContainer';
import DashboardSkeleton, {
  AverageCardSkeleton,
  ChartSkeleton,
  TableSkeleton,
} from '@/views/emr/lifestyle/shared/dashboard-components/DashboardSkeleton';
import MultiLineChart from '@/views/emr/lifestyle/shared/dashboard-components/MultiLineChart';
import NutritionBarChart from '@/views/emr/lifestyle/shared/dashboard-components/NutritionBarChart';
import NutritionLineChart from '@/views/emr/lifestyle/shared/dashboard-components/NutritionLineChart';
import NutritionSummaryTable from '@/views/emr/lifestyle/shared/dashboard-components/NutritionSummaryTable';
import PercentageChart from '@/views/emr/lifestyle/shared/dashboard-components/PercentageChart';

// Import reusable skeleton components

const NutritionDashboardView = () => {
  const { setSource } = lifestyleStore();
  const { patient } = useCurrentPatientStore();
  const { filterBy } = useLifestyleFilterStore();
  const {
    activeFilter,
    setActiveFilter,
    averageData,
    chartData,
    percentageData,
    macroSummary,
    microSummary,
    loading,
    loadingAverage,
    loadingCharts,
    loadingPercentage,
    loadingMacroSummary,
    loadingMicroSummary,
    setAverageData,
    setChartData,
    setPercentageData,
    setMacroSummary,
    setMicroSummary,
    setLoading,
    setLoadingAverage,
    setLoadingCharts,
    setLoadingPercentage,
    setLoadingMacroSummary,
    setLoadingMicroSummary,
    setError,
  } = useNutritionDashboardStore();

  // State for sorting
  const [macroSort, setMacroSort] = useState<{
    field: string;
    direction: 'asc' | 'desc';
  }>({ field: 'date', direction: 'desc' });
  const [microSort, setMicroSort] = useState<{
    field: string;
    direction: 'asc' | 'desc';
  }>({ field: 'date', direction: 'desc' });
  const [isFilterChanging, setIsFilterChanging] = useState(false);
  const [isMacroSorting, setIsMacroSorting] = useState(false);
  const [isMicroSorting, setIsMicroSorting] = useState(false);

  // Map lifestyle filter to nutrition dashboard filter
  const mapFilterType = (filter: string): FilterType => {
    switch (filter) {
      case 'SEVEN_DAYS':
        return 'last_7_days';
      case 'FIFTEEN_DAYS':
        return 'last_15_days';
      case 'ONE_MONTH':
        return 'last_month';
      default:
        return 'last_7_days';
    }
  };

  // Set the initial filter based on lifestyle filter
  const currentFilter = mapFilterType(filterBy.value);

  useEffect(() => {
    if (currentFilter !== activeFilter) {
      setIsFilterChanging(true);
      setActiveFilter(currentFilter);
    }
  }, [currentFilter, activeFilter, setActiveFilter]);

  // Reset source when component mounts
  useEffect(() => {
    setSource(null);
  }, [setSource]);

  const fetchData = async () => {
    if (!patient?.id) return;

    setLoading(true);
    setError(null);

    try {
      // Fetch average data
      setLoadingAverage(true);
      try {
        const avgData = await getNutritionAverage(patient.id, activeFilter);
        setAverageData(avgData);
      } catch (error) {
        console.error('Error fetching average data:', error);
      } finally {
        setLoadingAverage(false);
      }

      // Initial fetch for summary data (without sort parameters)
      setLoadingMacroSummary(true);
      setLoadingMicroSummary(true);
      try {
        const [macroData, microData] = await Promise.all([
          getNutritionSummary(patient.id, 'macro', activeFilter),
          getNutritionSummary(patient.id, 'micro', activeFilter),
        ]);

        setMacroSummary(macroData);
        setMicroSummary(microData);
      } catch (error) {
        console.error('Error fetching summary data:', error);
      } finally {
        setLoadingMacroSummary(false);
        setLoadingMicroSummary(false);
      }

      // Fetch chart data for all metrics
      setLoadingCharts(true);
      try {
        const [
          caloriesChart,
          carbsChart,
          proteinChart,
          fatChart,
          fiberChart,
          microChart,
          additionalChart,
        ] = await Promise.all([
          getNutritionChart(patient.id, activeFilter, 'calories'),
          getNutritionChart(patient.id, activeFilter, 'carbs'),
          getNutritionChart(patient.id, activeFilter, 'protein'),
          getNutritionChart(patient.id, activeFilter, 'fat'),
          getNutritionChart(patient.id, activeFilter, 'fiber'),
          getNutritionChart(patient.id, activeFilter, 'micro'),
          getNutritionChart(patient.id, activeFilter, 'additional'),
        ]);

        setChartData({
          calories: caloriesChart,
          carbs: carbsChart,
          protein: proteinChart,
          fat: fatChart,
          fiber: fiberChart,
          micro: microChart,
          additional: additionalChart,
        });
      } catch (error) {
        console.error('Error fetching chart data:', error);
      } finally {
        setLoadingCharts(false);
      }

      // Fetch percentage chart data
      setLoadingPercentage(true);
      try {
        const percentageChart = await getNutritionPercentageChart(
          patient.id,
          activeFilter
        );
        setPercentageData(percentageChart);
      } catch (error) {
        console.error('Error fetching percentage chart data:', error);
      } finally {
        setLoadingPercentage(false);
      }
    } catch (error) {
      console.error('Error fetching nutrition data:', error);
      setError('Failed to fetch nutrition data');
    } finally {
      setLoading(false);
      setIsFilterChanging(false);
    }
  };

  // Handle sort change for macro table
  const handleMacroSort = async (field: string, direction: 'asc' | 'desc') => {
    const newSort = { field, direction };
    setMacroSort(newSort);
    setIsMacroSorting(true);
    try {
      await fetchSortedData('macro', newSort);
    } finally {
      setIsMacroSorting(false);
    }
  };

  // Handle sort change for micro table
  const handleMicroSort = async (field: string, direction: 'asc' | 'desc') => {
    const newSort = { field, direction };
    setMicroSort(newSort);
    setIsMicroSorting(true);
    try {
      await fetchSortedData('micro', newSort);
    } finally {
      setIsMicroSorting(false);
    }
  };

  // Fetch sorted data for a specific table
  const fetchSortedData = async (
    type: 'macro' | 'micro',
    sort: { field: string; direction: 'asc' | 'desc' }
  ) => {
    if (!patient?.id) return;

    try {
      if (type === 'macro') {
        setLoadingMacroSummary(true);
        const macroData = await getNutritionSummary(
          patient.id,
          'macro',
          activeFilter,
          sort.field,
          sort.direction
        );
        setMacroSummary(macroData);
      } else {
        setLoadingMicroSummary(true);
        const microData = await getNutritionSummary(
          patient.id,
          'micro',
          activeFilter,
          sort.field,
          sort.direction
        );
        setMicroSummary(microData);
      }
    } catch (error) {
      console.error(`Error fetching ${type} sorted data:`, error);
      setError(`Failed to fetch ${type} sorted data`);
    } finally {
      if (type === 'macro') {
        setLoadingMacroSummary(false);
      } else {
        setLoadingMicroSummary(false);
      }
    }
  };

  // Initial data fetch and refetch when filter changes
  useEffect(() => {
    fetchData();
  }, [patient?.id, activeFilter]);

  // Show full skeleton only on initial load when no data exists
  const showFullSkeleton =
    loading &&
    !averageData &&
    !chartData.calories &&
    !macroSummary &&
    !microSummary;

  if (showFullSkeleton) {
    return <DashboardSkeleton />;
  }

  // Determine grid classes based on filter
  const isLastMonth = activeFilter === 'last_month';

  // Legend items for Macro-nutrients
  const macroLegendItems = [
    { color: '#FFAA5A', label: 'Carbs' },
    { color: '#A8C256', label: 'Protein' },
    { color: '#0B3948', label: 'Fat' },
    { color: '#AB92BF', label: 'Fiber' },
    { color: '#1B998B', label: 'Percentage' },
  ];

  // Render macro-nutrients charts
  const renderMacroNutrientsCharts = () => {
    const chartGridClass = isLastMonth
      ? 'grid grid-cols-1 gap-3 p-3'
      : 'grid grid-cols-1 lg:grid-cols-2 gap-3 p-3';

    return (
      <ChartContainer
        title="Macro-nutrients (gms)"
        legendItems={macroLegendItems}
        showShadow={true}
      >
        <div className={chartGridClass}>
          {/* Carbohydrates Chart */}
          {loadingCharts || (isFilterChanging && !chartData.carbs) ? (
            <ChartSkeleton />
          ) : chartData.carbs ? (
            <ChartContainer>
              <NutritionBarChart
                data={chartData.carbs.chartData}
                dataKey="carbs"
                colors={{ top: '#FF7C00', bottom: '#FFAA5A' }}
                title="Carbohydrates"
              />
            </ChartContainer>
          ) : null}

          {/* Protein Chart */}
          {loadingCharts || (isFilterChanging && !chartData.protein) ? (
            <ChartSkeleton />
          ) : chartData.protein ? (
            <ChartContainer>
              <NutritionBarChart
                data={chartData.protein.chartData}
                dataKey="protein"
                colors={{ top: '#90AA3E', bottom: '#A7C155' }}
                title="Protein"
                unit="g"
              />
            </ChartContainer>
          ) : null}

          {/* Fat Chart */}
          {loadingCharts || (isFilterChanging && !chartData.fat) ? (
            <ChartSkeleton />
          ) : chartData.fat ? (
            <ChartContainer>
              <NutritionBarChart
                data={chartData.fat.chartData}
                dataKey="fat"
                colors={{ top: '#0C3B4A', bottom: '#197E9E' }}
                title="Fat"
                unit="g"
              />
            </ChartContainer>
          ) : null}

          {/* Fiber Chart */}
          {loadingCharts || (isFilterChanging && !chartData.fiber) ? (
            <ChartSkeleton />
          ) : chartData.fiber ? (
            <ChartContainer>
              <NutritionBarChart
                data={chartData.fiber.chartData}
                dataKey="fiber"
                colors={{ top: '#795794', bottom: '#AB92BF' }}
                title="Fiber"
                unit="g"
              />
            </ChartContainer>
          ) : null}

          {/* Percentage Chart - Full width */}
          {loadingPercentage || isFilterChanging ? (
            <div className={isLastMonth ? '' : 'lg:col-span-2'}>
              <ChartSkeleton height="h-64" />
            </div>
          ) : percentageData ? (
            <div className={isLastMonth ? '' : 'lg:col-span-2'}>
              <ChartContainer>
                <PercentageChart
                  data={percentageData.chartData}
                  dataKeys={[
                    {
                      key: 'protein_percentage',
                      name: 'Protein',
                      color: '#A8C256',
                      radius: [2, 2, 0, 0] as [number, number, number, number],
                    },
                    {
                      key: 'carbs_percentage',
                      name: 'Carbs',
                      color: '#FFAA5A',
                      radius: [0, 0, 0, 0] as [number, number, number, number],
                    },
                    {
                      key: 'fat_percentage',
                      name: 'Fat',
                      color: '#0B3948',
                      radius: [0, 0, 2, 2] as [number, number, number, number],
                    },
                  ]}
                  title="Percentage Chart"
                  maxBarWidth={40}
                />
              </ChartContainer>
            </div>
          ) : null}
        </div>
      </ChartContainer>
    );
  };

  return (
    <div className="h-full flex flex-col p-4 bg-gray-50 overflow-y-auto">
      {/* Summary Section */}
      <div className="mb-6">
        {/* Summary Header */}
        <h2 className="text-lg font-medium text-gray-900 mb-4">Summary</h2>

        {/* First Row - Average Cards */}
        <div className="grid grid-cols-4 gap-5 mb-4">
          {loadingAverage || (isFilterChanging && !averageData) ? (
            <>
              <AverageCardSkeleton />
              <AverageCardSkeleton />
              <AverageCardSkeleton />
              <AverageCardSkeleton />
            </>
          ) : averageData ? (
            <>
              <AverageCard
                title="Avg. Calories"
                value={averageData.averages.calories}
                unit="kcal"
                icon={
                  <img
                    src={CaloryIcon.src}
                    alt="Calories"
                    width={24}
                    height={24}
                  />
                }
                bgColor="#FAEBEE"
                borderColor="#BA324F"
              />
              <AverageCard
                title="Avg. Carbohydrates"
                value={averageData.averages.carbs}
                unit="gm"
                icon={
                  <img
                    src={CarbohydrateIcon.src}
                    alt="Carbohydrates"
                    width={24}
                    height={24}
                  />
                }
                bgColor="#FFF2E5"
                borderColor="#FFAA5A"
              />
              <AverageCard
                title="Avg. Protein"
                value={averageData.averages.protein}
                unit="gm"
                icon={
                  <img
                    src={ProteinIcon.src}
                    alt="Protein"
                    width={24}
                    height={24}
                  />
                }
                bgColor="#E3F2FD"
                borderColor="#1E88E5"
              />
              <AverageCard
                title="Avg. Fat"
                value={averageData.averages.fat}
                unit="gm"
                icon={
                  <img src={FatIcon.src} alt="Fat" width={24} height={24} />
                }
                bgColor="#F3E5F5"
                borderColor="#8E24AA"
              />
            </>
          ) : null}
        </div>

        {/* Second Row - Average Cards */}
        <div className="grid grid-cols-4 gap-5">
          {loadingAverage || (isFilterChanging && !averageData) ? (
            <>
              <AverageCardSkeleton />
              <AverageCardSkeleton />
              <AverageCardSkeleton />
              <AverageCardSkeleton />
            </>
          ) : averageData ? (
            <>
              <AverageCard
                title="Avg. Fibre"
                value={averageData.averages.fiber}
                unit="gm"
                icon={
                  <img src={FiberIcon.src} alt="Fiber" width={24} height={24} />
                }
                bgColor="#F3EFF6"
                borderColor="#AB92BF"
              />
              <AverageCard
                title="Avg. Sugar"
                value={averageData.averages.sugar}
                unit="gm"
                icon={
                  <img src={SugarIcon.src} alt="Sugar" width={24} height={24} />
                }
                bgColor="#E9FBF9"
                borderColor="#1B998B"
              />
              <AverageCard
                title="Avg. Salt"
                value={averageData.averages.salt}
                unit="gm"
                icon={
                  <img src={SaltIcon.src} alt="Salt" width={24} height={24} />
                }
                bgColor="#FDEDE7"
                borderColor="#F26430"
              />
              <AverageCard
                title="Avg. Oil"
                value={averageData.averages.oil}
                unit="ml"
                icon={
                  <img src={OilIcon.src} alt="Oil" width={24} height={24} />
                }
                bgColor="#E5FCFF"
                borderColor="#00ACC1"
              />
            </>
          ) : null}
        </div>
      </div>

      {/* Summary Tables in Separate Accordions */}
      <div className="mb-6 space-y-4">
        {/* Macronutrients Accordion Table */}
        {(loadingMacroSummary && !isMacroSorting) ||
        (isFilterChanging && !macroSummary) ? (
          <TableSkeleton />
        ) : (
          <AccordionTable title="Macro-Nutrients" defaultExpanded={false}>
            <div className="p-1">
              <NutritionSummaryTable
                data={macroSummary || []}
                type="macro"
                onSort={handleMacroSort}
                sortField={macroSort.field}
                sortDirection={macroSort.direction}
                loading={isMacroSorting}
              />
            </div>
          </AccordionTable>
        )}

        {/* Micronutrients Accordion Table */}
        {(loadingMicroSummary && !isMicroSorting) ||
        (isFilterChanging && !microSummary) ? (
          <TableSkeleton />
        ) : (
          <AccordionTable title="Micro-Nutrients" defaultExpanded={false}>
            <div className="p-1">
              <NutritionSummaryTable
                data={microSummary || []}
                type="micro"
                onSort={handleMicroSort}
                sortField={microSort.field}
                sortDirection={microSort.direction}
                loading={isMicroSorting}
              />
            </div>
          </AccordionTable>
        )}
      </div>

      {/* Calories Chart */}
      <div className="mb-6">
        {loadingCharts || (isFilterChanging && !chartData.calories) ? (
          <ChartSkeleton />
        ) : chartData.calories ? (
          <ChartContainer>
            <NutritionLineChart
              data={chartData.calories.chartData}
              dataKey="calories"
              color="#BA324F"
              title="Calories"
              unit="kcal"
            />
          </ChartContainer>
        ) : null}
      </div>

      {/* Macro-nutrients Charts Section */}
      <div className="mb-6">{renderMacroNutrientsCharts()}</div>

      {/* Multi-line Charts */}
      <div className="grid grid-cols-1 gap-6 mb-6">
        {/* Micro-nutrients Chart */}
        {loadingCharts || (isFilterChanging && !chartData.micro) ? (
          <ChartSkeleton />
        ) : chartData.micro ? (
          <ChartContainer>
            <MultiLineChart
              title="Micro-nutrients"
              data={chartData.micro.chartData}
              lines={[
                { dataKey: 'calcium', color: '#1B998B', name: 'Calcium' },
                {
                  dataKey: 'phosphorus',
                  color: '#FFAA5A',
                  name: 'Phosphorous',
                },
                { dataKey: 'iron', color: '#AB92BF', name: 'Iron' },
                { dataKey: 'magnesium', color: '#BA324F', name: 'Magnesium' },
                { dataKey: 'sodium', color: '#A8C256', name: 'Sodium' },
                { dataKey: 'potassium', color: '#0B3948', name: 'Potassium' },
              ]}
              unit="mg"
              showClearFilter={true}
            />
          </ChartContainer>
        ) : null}

        {/* Sugar & Salt Chart */}
        {loadingCharts || (isFilterChanging && !chartData.additional) ? (
          <ChartSkeleton />
        ) : chartData.additional ? (
          <ChartContainer>
            <MultiLineChart
              data={chartData.additional.chartData}
              lines={[
                { dataKey: 'salt', color: '#F26430', name: 'Salt' },
                { dataKey: 'sugar', color: '#1B998B', name: 'Sugar' },
              ]}
              title="Sugar & Salt"
              unit="gm"
            />
          </ChartContainer>
        ) : null}
      </div>
    </div>
  );
};

export default memo(NutritionDashboardView);
