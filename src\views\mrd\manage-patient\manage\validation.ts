import * as yup from 'yup';

import { aadharValidationYup } from '@/utils/mrd/manage-patient/aadhar';
import {
  panValidationYup,
  passportValidationYup,
} from '@/utils/mrd/manage-patient/validation';

// Extend yup string type
declare module 'yup' {
  interface StringSchema {
    validDate(message?: string): this;
    pastDate(message?: string): this;
    aadharNumber(message?: string): this;
    panNumber(message?: string): this;
    passportNumber(message?: string): this;
  }
}

yup.addMethod(
  yup.string,
  'validDate',
  function (message = 'Invalid date format') {
    return this.test('valid-date', message, function (value) {
      if (!value) return true;
      return !isNaN(Date.parse(value));
    });
  }
);

// Add pastDate validation method
yup.addMethod(
  yup.string,
  'pastDate',
  function (message = 'Date cannot be in the future') {
    return this.test('past-date', message, function (value) {
      if (!value) return true;
      const selectedDate = new Date(value);
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Reset time to start of day
      return selectedDate <= today;
    });
  }
);

yup.addMethod(
  yup.string,
  'aadharNumber',
  function (message = 'Invalid aadhar number') {
    return this.test('valid-aadhar', message, function (value) {
      if (!value) return true;
      return aadharValidationYup(value);
    });
  }
);

yup.addMethod(
  yup.string,
  'panNumber',
  function (message = 'Invalid PAN number') {
    return this.test('valid-pan', message, function (value) {
      if (!value) return true;
      return panValidationYup(value);
    });
  }
);

yup.addMethod(
  yup.string,
  'passportNumber',
  function (message = 'Invalid passport number') {
    return this.test('valid-passport', message, function (value) {
      if (!value) return true;
      return passportValidationYup(value);
    });
  }
);

const generalDetailsSchema = yup.object().shape({
  name: yup.string().required('Required'),
  dob: yup
    .string()
    .required('Required')
    .validDate('Invalid date format')
    .pastDate('Date of birth cannot be in the future'),
  sex: yup.object().required('Required'),
  height: yup
    .string()
    .test('is-positive', 'Height must be a positive number', (value) => {
      if (!value) return true;
      return Number(value) > 0;
    })
    .required('Required'),
  weight: yup
    .string()
    .test('is-positive', 'Weight must be a positive number', (value) => {
      if (!value) return true;
      return Number(value) > 0;
    })
    .required('Required'),
  maritalStatus: yup.object().nullable(),
  age: yup.string().required('Required'),

  address: yup.object().shape({
    houseName: yup.string(),
    street: yup.string(),
    city: yup.string().required('Required'),
    district: yup.string(),
    state: yup.object().nullable().optional(),
    country: yup.object().nullable().optional(),
    pin: yup
      .string()
      .matches(/^[0-9]{6}$/, 'PIN must be 6 digits')
      .optional(),
  }),

  contact: yup.object().shape({
    phone: yup
      .string()
      .required('Required')
      .test('masked-or-valid', 'Must be 10 digits', (value) => {
        if (!value) return false;
        if (value === '******') return true;
        return /^[0-9]{10}$/.test(value);
      }),
    email: yup
      .string()
      .nullable()
      .test('masked-or-valid-email', 'Invalid email address', (value) => {
        if (!value) return true;
        if (value === '******') return true;
        return yup.string().email().isValidSync(value);
      }),
  }),
});

const verificationSchema = yup.object().shape({
  proof: yup.object().shape({
    type: yup.object().nullable().optional(),
    aadharNumber: yup
      .string()
      .test('conditional-aadhar', 'Invalid aadhar number', function (value) {
        const proofType = this.parent.type?.value;

        if (value?.includes('*')) {
          return true;
        }

        if (value && value.trim() !== '') {
          if (proofType === 'Pan card' || proofType === 'Passport') {
            return true;
          }

          return aadharValidationYup(value);
        }

        return true;
      })
      .optional(),
    panNumber: yup
      .string()
      .test('conditional-pan', 'Invalid PAN number', function (value) {
        const proofType = this.parent.type?.value;

        if (value?.includes('*')) {
          return true;
        }

        if (proofType === 'Pan card' && value && value.trim() !== '') {
          return panValidationYup(value);
        }

        return true;
      })
      .optional(),
    passportNumber: yup
      .string()
      .test(
        'conditional-passport',
        'Invalid passport number',
        function (value) {
          const proofType = this.parent.type?.value;

          if (value?.includes('*')) {
            return true;
          }

          if (proofType === 'Passport' && value && value.trim() !== '') {
            return passportValidationYup(value);
          }

          return true;
        }
      )
      .optional(),
    abhaNumber: yup.string().optional(),
    url: yup.mixed().optional(),
  }),
});

const insuranceSchema = yup.object().shape({
  provider: yup.object().nullable().optional(),
  insuranceId: yup.string().optional(),
  url: yup.mixed().optional(),
});

const finalSchema = yup.object().shape({});

export const tabValidationSchemas = {
  0: generalDetailsSchema,
  1: verificationSchema,
  2: insuranceSchema,
  3: finalSchema,
};
