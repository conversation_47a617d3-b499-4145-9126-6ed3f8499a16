'use client';

import { memo, useEffect, useState } from 'react';

import Loading from '@/lib/common/loading';

import { useCurrentPatientStore } from '@/store/currentPatientStore';
import { useLifestyleFilterStore } from '@/store/emr/lifestyle/filter-store';
import { lifestyleStore } from '@/store/emr/lifestyle/lifestyle-store';

import { getPhysicalActivityDashboard } from '@/query/emr/lifestyle/physical-activity-dashboard';

import PhysicalDashboardSummary, {
  PhysicalSummary,
} from '@/views/emr/lifestyle/physical/dashboard/PhysicalDashboardSummary';
import AccordionTable from '@/views/emr/lifestyle/shared/dashboard-components/AccordionTable';
import ChartContainer from '@/views/emr/lifestyle/shared/dashboard-components/ChartContainer';
import {
  AverageCardSkeleton,
  ChartSkeleton,
  TableSkeleton,
} from '@/views/emr/lifestyle/shared/dashboard-components/DashboardSkeleton';

import ActivityPieChart from './ActivityPieChart';
import ActivityTypePercentage<PERSON>hart from './ActivityTypePercentageChart';
import PhysicalDuration<PERSON>ine<PERSON>hart from './PhysicalDurationLineChart';
import PhysicalMetMinutesBarChart from './PhysicalMetMinutesBarChart';

const PhysicalActivityDashboardView = () => {
  const { setSource } = lifestyleStore();
  const { patient } = useCurrentPatientStore();
  const { fromDate, toDate } = useLifestyleFilterStore();
  const [summary, setSummary] = useState<PhysicalSummary | null>(null);
  const [activityRecords, setActivityRecords] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [isLastMonth, setIsLastMonth] = useState(false);
  const [metMinutesData, setMetMinutesData] = useState<any[]>([]);
  const [durationData, setDurationData] = useState<any[]>([]);
  const [activityTypeData, setActivityTypeData] = useState<any[]>([]);
  const [activityDistribution, setActivityDistribution] = useState<any[]>([]);
  const [intensityDistribution, setIntensityDistribution] = useState<any[]>([]);
  const [intensityData, setIntensityData] = useState<any[]>([]);

  let daysToShow = 30;
  if (toDate && fromDate) {
    const diff =
      Math.ceil(
        (new Date(toDate).getTime() - new Date(fromDate).getTime()) /
          (1000 * 60 * 60 * 24)
      ) + 1;
    daysToShow = diff;
  }
  const metMinutesDataSliced = metMinutesData.slice(-daysToShow);
  const durationDataSliced = durationData.slice(-daysToShow);

  useEffect(() => {
    setSource(null);
  }, [setSource]);

  useEffect(() => {
    const fetchData = async () => {
      if (!patient?.id) return;
      setLoading(true);
      try {
        const response = await getPhysicalActivityDashboard(
          patient.id,
          fromDate,
          toDate,
          ''
        );

        setSummary(response.summary);
        if (response.charts) {
          if (response.charts.metMinutes) {
            console.log('Setting metMinutesData:', response.charts.metMinutes);
            setMetMinutesData(response.charts.metMinutes);
          } else {
            setMetMinutesData([]);
          }

          console.log(
            'Total Duration data from API:',
            response.charts.totalDuration || 'No data'
          );
          setDurationData(response.charts.totalDuration || []);
        }

        const startDate = new Date(fromDate);
        const endDate = new Date(toDate);
        const allDatesInRange = [];

        const currentDate = new Date(startDate);
        while (currentDate <= endDate) {
          allDatesInRange.push(new Date(currentDate));
          currentDate.setDate(currentDate.getDate() + 1);
        }

        const formattedData = allDatesInRange.map((date) => {
          const dateString = date.toISOString().split('T')[0];
          const formattedDate = date.toLocaleDateString('en-US', {
            day: 'numeric',
            month: 'short',
          });

          type ActivityData = {
            Aerobics?: number;
            Balance?: number;
            Strength?: number;
            Flexibility?: number;
          };

          let dayData: ActivityData = {};
          if (response.dayWiseData) {
            const matchingDay = response.dayWiseData.find(
              (d: any) => d.date === dateString
            );
            if (matchingDay && matchingDay.activityTypePercentages) {
              dayData = matchingDay.activityTypePercentages as ActivityData;
            }
          }

          return {
            date: formattedDate,
            Aerobics: dayData.Aerobics || 0,
            Balance: dayData.Balance || 0,
            Strength: dayData.Strength || 0,
            Flexibility: dayData.Flexibility || 0,
            fullDate: dateString,
          };
        });

        console.log('Formatted activity type data:', formattedData);
        setActivityTypeData(formattedData);

        if (response.activityDistribution) {
          const activityColors: { [key: string]: string } = {
            Aerobics: '#BA324F',
            Balance: '#1B998B',
            Strength: '#A8C256',
            Flexibility: '#0B3948',
          };

          const formattedActivityData = response.activityDistribution.map(
            (item: any) => ({
              name: item.activityType,
              value: item.percentage,
              percentage: item.percentage,
              duration: item.duration,
              count: item.count,
              color: activityColors[item.activityType] || '#999999',
            })
          );
          setActivityDistribution(formattedActivityData);
        }

        if (response.intensityDistribution) {
          const intensityColors: { [key: string]: string } = {
            Intense: '#BA324F',
            Moderate: '#FFAA5A',

            Mild: '#AB92BF',
          };

          const formattedIntensityData = response.intensityDistribution.map(
            (item: any) => ({
              name: item.intensity,
              value: item.percentage,
              percentage: item.percentage,
              duration: item.duration,
              color: intensityColors[item.intensity] || '#999999',
            })
          );
          setIntensityDistribution(formattedIntensityData);
        }

        const formattedIntensityBarData = allDatesInRange.map((date) => {
          const dateObj = new Date(date);
          const dateString = dateObj.toISOString().split('T')[0];
          const formattedDate = dateObj.toLocaleDateString('en-US', {
            day: 'numeric',
            month: 'short',
            year: 'numeric',
          });

          type IntensityData = {
            Intense?: number;

            Moderate?: number;

            Mild?: number;
          };

          let dayData: IntensityData = {};
          if (response.dayWiseData) {
            const matchingDay = response.dayWiseData.find(
              (d: any) => d.date === dateString
            );
            if (matchingDay && matchingDay.intensityPercentages) {
              dayData = matchingDay.intensityPercentages as IntensityData;
            }
          }

          return {
            date: dateObj.toISOString(),
            Intense: dayData.Intense || 0,
            Moderate: dayData.Moderate || 0,
            Mild: dayData.Mild || 0,
            fullDate: dateString,
          };
        });
        setIntensityData(formattedIntensityBarData);

        if (response.activityRecords) {
          const sorted = [...response.activityRecords].sort(
            (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
          );
          const uniqueDates = Array.from(new Set(sorted.map((r) => r.date)));
          const recentDates = uniqueDates
            .sort((a, b) => new Date(b).getTime() - new Date(a).getTime())
            .slice(0, 2);
          const grouped = recentDates.map((date) => ({
            date,
            records: sorted.filter((r) => r.date === date),
          }));
          setActivityRecords(grouped);
        } else {
          setActivityRecords([]);
        }
      } catch (_error) {
        setSummary(null);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [patient?.id, fromDate, toDate]);

  return (
    <div className="h-full flex flex-col p-2 overflow-y-auto">
      {loading ? (
        <div className="h-full flex flex-col p-4 bg-gray-50 overflow-y-auto">
          <div className="mb-6">
            <div className="h-7 w-48 bg-gray-200 rounded animate-pulse mb-4"></div>
            <div className="grid grid-cols-3 gap-5 mb-4">
              {[...Array(3)].map((_, i) => (
                <AverageCardSkeleton key={i} />
              ))}
            </div>
          </div>
          <div className="mb-6">
            <div className="h-7 w-48 bg-gray-200 rounded animate-pulse mb-4"></div>
            <TableSkeleton />
          </div>

          <div className="space-y-6">
            <div className="h-7 w-48 bg-gray-200 rounded animate-pulse mb-4"></div>
            <div className="grid grid-cols-1 gap-6">
              <ChartSkeleton height="h-64" />
              <ChartSkeleton height="h-64" />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <ChartSkeleton height="h-64" />
                <ChartSkeleton height="h-64" />
              </div>
              <ChartSkeleton height="h-64" />
            </div>
          </div>
        </div>
      ) : summary ? (
        <>
          <PhysicalDashboardSummary summary={summary} />
          <div className="mt-2">
            <AccordionTable title="Activity" defaultExpanded>
              <div className="m-3 rounded-lg border border-gray-200 bg-white overflow-hidden">
                <table
                  className="min-w-full text-sm text-left border-separate"
                  style={{ borderSpacing: 0 }}
                >
                  <thead>
                    <tr className="bg-[#E6F6FF]">
                      <th className="px-2 py-3 border-b border-r border-[#DAE1E7] text-gray-600 font-medium">
                        Date
                      </th>
                      <th className="px-2 py-3 border-b border-r border-[#DAE1E7] text-gray-600 font-medium">
                        Activity Type
                      </th>
                      <th className="px-2 py-3 border-b border-r border-[#DAE1E7] text-gray-600 font-medium">
                        Activity
                      </th>
                      <th className="px-2 py-3 border-b border-r border-[#DAE1E7] text-gray-600 font-medium">
                        Intensity
                      </th>
                      <th className="px-2 py-3 border-b border-r border-[#DAE1E7] text-gray-600 font-medium text-center">
                        Duration(mins)
                      </th>
                      <th className="px-2 py-3 border-b border-[#DAE1E7] text-gray-600 font-medium text-center">
                        Calories Spent(kcal)
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {loading ? (
                      <tr>
                        <td colSpan={6} className="py-4 text-center">
                          <Loading />
                        </td>
                      </tr>
                    ) : activityRecords.length === 0 ? (
                      <tr>
                        <td
                          colSpan={6}
                          className="py-4 text-center text-gray-500"
                        >
                          No activity records found
                        </td>
                      </tr>
                    ) : (
                      activityRecords.map(
                        (
                          group: { date: string; records: any[] },
                          groupIdx: number
                        ) =>
                          group.records.map((record, recIdx) => (
                            <tr key={`${group.date}-${recIdx}`}>
                              {recIdx === 0 && (
                                <td
                                  rowSpan={group.records.length}
                                  className="px-2 py-2 border-b border-r border-[#DAE1E7] text-gray-700 whitespace-nowrap"
                                >
                                  {new Date(group.date).toLocaleDateString(
                                    'en-US',
                                    {
                                      month: 'short',
                                      day: 'numeric',
                                      year: 'numeric',
                                    }
                                  )}
                                </td>
                              )}
                              <td className="px-2 py-2 border-b border-r border-[#DAE1E7] text-gray-700">
                                {record.activityType}
                              </td>
                              <td className="px-2 py-2 border-b border-r border-[#DAE1E7] text-gray-700">
                                {record.activity}
                              </td>
                              <td className="px-2 py-2 border-b border-r border-[#DAE1E7] text-gray-700">
                                {record.intensity}
                              </td>
                              <td className="px-2 py-2 border-b border-r border-[#DAE1E7] text-gray-700  text-center">
                                {record.duration}
                              </td>
                              <td className="px-2 py-2 border-b border-[#DAE1E7] text-gray-700 text-center">
                                {record.caloriesBurned}
                              </td>
                            </tr>
                          ))
                      )
                    )}
                  </tbody>
                </table>
              </div>
            </AccordionTable>

            <div className="mt-2">
              <h2 className="text-[18px] font-semibold mb-2">
                Physical Visualization
              </h2>
              <div className="border border-gray-200 rounded-lg p-1 mx-2 mb-4">
                <PhysicalMetMinutesBarChart data={metMinutesDataSliced} />
              </div>
              <div className="border border-gray-200 rounded-lg p-1 mx-2 mb-4">
                <PhysicalDurationLineChart data={durationDataSliced} />
              </div>

              <div className="mt-4">
                {loading ? (
                  <div className="h-64">
                    <ChartSkeleton />
                  </div>
                ) : activityTypeData.length > 0 ? (
                  <ChartContainer className="mt-4">
                    <ActivityTypePercentageChart
                      data={activityTypeData}
                      dataKeys={[
                        {
                          key: 'Aerobics',
                          name: 'Aerobics',
                          color: '#BA324F',
                          radius: [0, 0, 0, 0] as [
                            number,
                            number,
                            number,
                            number,
                          ],
                        },
                        {
                          key: 'Balance',
                          name: 'Balance',
                          color: '#1B998B',
                          radius: [0, 0, 0, 0] as [
                            number,
                            number,
                            number,
                            number,
                          ],
                        },
                        {
                          key: 'Strength',
                          name: 'Strength',
                          color: '#A8C256',
                          radius: [0, 0, 0, 0] as [
                            number,
                            number,
                            number,
                            number,
                          ],
                        },
                        {
                          key: 'Flexibility',
                          name: 'Flexibility',
                          color: '#0B3948',
                          radius: [2, 2, 0, 0] as [
                            number,
                            number,
                            number,
                            number,
                          ],
                        },
                      ]}
                      title="Activity Type"
                      showLegend={true}
                      maxBarWidth={40}
                    />
                  </ChartContainer>
                ) : (
                  <div className="mt-4 text-center text-gray-500">
                    No activity type data available
                  </div>
                )}
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-2 px-2">
                <ActivityPieChart
                  data={activityDistribution}
                  title="Activity Distribution"
                  unit="mins"
                />
                <ActivityPieChart
                  data={intensityDistribution}
                  title="Intensity Distribution"
                  unit="mins"
                />
              </div>

              {intensityData.length > 0 && (
                <ChartContainer className="mt-4">
                  <ActivityTypePercentageChart
                    data={intensityData}
                    dataKeys={[
                      {
                        key: 'Mild',
                        name: 'Mild',
                        color: '#AB92BF',
                        radius: [2, 2, 0, 0] as [
                          number,
                          number,
                          number,
                          number,
                        ],
                      },
                      {
                        key: 'Moderate',
                        name: 'Moderate',
                        color: '#FFAA5A',
                        radius: [0, 0, 0, 0] as [
                          number,
                          number,
                          number,
                          number,
                        ],
                      },
                      {
                        key: 'Intense',
                        name: 'Intense',
                        color: '#BA324F',
                        radius: [0, 0, 2, 2] as [
                          number,
                          number,
                          number,
                          number,
                        ],
                      },
                    ]}
                    title="Intensity Type"
                    showLegend={true}
                    maxBarWidth={40}
                  />
                </ChartContainer>
              )}
            </div>
          </div>
        </>
      ) : (
        <div>No data available</div>
      )}
    </div>
  );
};

export default memo(PhysicalActivityDashboardView);
