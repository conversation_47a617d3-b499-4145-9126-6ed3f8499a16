import React, { useState, useMemo, useEffect } from 'react';

import { ArrowUp, ArrowDown } from 'lucide-react';

import { NutritionSummaryDataPoint } from '@/query/emr/lifestyle/nutrition-dashboard';

import TableV2 from '@/core/components/table-v2';
import { HeaderV2, RowV2, CellV2 } from '@/core/components/table-v2/types';

interface NutritionSummaryTableProps {
  data: NutritionSummaryDataPoint[];
  type: 'macro' | 'micro';
  onSort?: (field: string, direction: 'asc' | 'desc') => void;
  sortField?: string;
  sortDirection?: 'asc' | 'desc';
  className?: string;
  loading?: boolean;
}

const NutritionSummaryTable: React.FC<NutritionSummaryTableProps> = ({
  data,
  type,
  onSort,
  sortField: propSortField = 'date',
  sortDirection: propSortDirection = 'desc',
  className = '',
  loading = false,
}) => {
  const [sortField, setSortField] = useState<string>(propSortField);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>(
    propSortDirection
  );

  // Sync with props if they change
  useEffect(() => {
    setSortField(propSortField);
    setSortDirection(propSortDirection);
  }, [propSortField, propSortDirection]);

  const handleSort = (field: string) => {
    const newDirection =
      sortField === field && sortDirection === 'desc' ? 'asc' : 'desc';
    setSortField(field);
    setSortDirection(newDirection);
    if (onSort) {
      onSort(field, newDirection);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: '2-digit',
    });
  };

  const formatValue = (
    value: number | string | undefined | null,
    decimals: number = 1
  ) => {
    if (value === undefined || value === null || value === '') return '---';
    // Convert string to number if needed
    const numValue = typeof value === 'string' ? parseFloat(value) : value;
    if (isNaN(numValue)) return '---';
    return numValue.toFixed(decimals);
  };

  // Utility function to format macro nutrient cells with percentage
  const formatMacroCell = (
    value: number | undefined,
    percentage: number | undefined
  ) => {
    if (value === undefined || percentage === undefined) return '---';

    return (
      <div className="flex flex-col gap-2.5">
        <span>{formatValue(value)}g</span>
        <span>({formatValue(percentage)}%)</span>
      </div>
    );
  };

  const macroHeaders: HeaderV2[] = [
    {
      key: 'date',
      header: (
        <div
          className="flex items-center justify-center gap-3 cursor-pointer text-gray-700 hover:text-gray-900 transition-colors"
          onClick={() => handleSort('date')}
        >
          Date
          {sortField === 'date' &&
            (sortDirection === 'asc' ? (
              <ArrowUp size={14} />
            ) : (
              <ArrowDown size={14} />
            ))}
        </div>
      ),
      cellProps: {
        align: 'center',
        sx: {
          minWidth: '85px',
          width: '85px',
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.02)',
          },
        },
      },
    },
    {
      key: 'meals',
      header: 'Meals',
      cellProps: {
        align: 'center',
        sx: {
          minWidth: '45px',
          width: '45px',
        },
      },
    },
    {
      key: 'snacks',
      header: 'Snacks',
      cellProps: {
        align: 'center',
        sx: {
          minWidth: '50px',
          width: '50px',
        },
      },
    },
    {
      key: 'calories',
      header: (
        <div style={{ textAlign: 'center' }}>
          Calories
          <br />
          (kcal)
        </div>
      ),
      cellProps: { align: 'center' },
    },
    {
      key: 'carbs',
      header: (
        <div style={{ textAlign: 'center' }}>
          Carbs
          <br />
          (g/%)
        </div>
      ),
      cellProps: {
        align: 'center',
        sx: {
          minWidth: '75px',
          width: '75px',
          py: 1.5,
        },
      },
    },
    {
      key: 'protein',
      header: (
        <div style={{ textAlign: 'center' }}>
          Protein
          <br />
          (g/%)
        </div>
      ),
      cellProps: {
        align: 'center',
        sx: {
          minWidth: '75px',
          width: '75px',
          py: 1.5,
        },
      },
    },
    {
      key: 'fat',
      header: (
        <div style={{ textAlign: 'center' }}>
          Fat
          <br />
          (g/%)
        </div>
      ),
      cellProps: {
        align: 'center',
        sx: {
          minWidth: '75px',
          width: '75px',
          py: 1.5,
        },
      },
    },
    {
      key: 'sfa',
      header: (
        <div style={{ textAlign: 'center' }}>
          SFA
          <br />
          (mg)
        </div>
      ),
      cellProps: {
        align: 'center',
        sx: {
          minWidth: '58px',
          width: '62px',
        },
      },
    },
    {
      key: 'mufa',
      header: (
        <div style={{ textAlign: 'center' }}>
          MUFA
          <br />
          (mg)
        </div>
      ),
      cellProps: {
        align: 'center',
        sx: {
          minWidth: '58px',
          width: '62px',
        },
      },
    },
    {
      key: 'pufa',
      header: (
        <div style={{ textAlign: 'center' }}>
          PUFA
          <br />
          (mg)
        </div>
      ),
      cellProps: {
        align: 'center',
        sx: {
          minWidth: '58px',
          width: '64px',
        },
      },
    },
    {
      key: 'cholesterol',
      header: (
        <div style={{ textAlign: 'center' }}>
          Cholesterol
          <br />
          (mg)
        </div>
      ),
      cellProps: { align: 'center' },
    },
    {
      key: 'fiber',
      header: (
        <div style={{ textAlign: 'center' }}>
          Fiber
          <br />
          (g)
        </div>
      ),
      cellProps: { align: 'center' },
    },
    {
      key: 'sugar',
      header: (
        <div style={{ textAlign: 'center' }}>
          Sugar
          <br />
          (mg)
        </div>
      ),
      cellProps: {
        align: 'center',
        sx: {
          minWidth: '55px',
          width: '55px',
        },
      },
    },
    {
      key: 'oil',
      header: (
        <div style={{ textAlign: 'center' }}>
          Oil
          <br />
          (ml)
        </div>
      ),
      cellProps: {
        align: 'center',
        sx: {
          minWidth: '42px',
          width: '42px',
        },
      },
    },
    {
      key: 'salt',
      header: (
        <div style={{ textAlign: 'center' }}>
          Salt
          <br />
          (mg)
        </div>
      ),
      cellProps: {
        align: 'center',
        sx: {
          minWidth: '45px',
          width: '45px',
        },
      },
    },
  ];

  const microHeaders: HeaderV2[] = [
    {
      key: 'date',
      header: (
        <div
          className="flex items-center justify-center gap-3 cursor-pointer text-gray-700 hover:text-gray-900 transition-colors"
          onClick={() => handleSort('date')}
        >
          Date
          {sortField === 'date' &&
            (sortDirection === 'asc' ? (
              <ArrowUp size={14} />
            ) : (
              <ArrowDown size={14} />
            ))}
        </div>
      ),
      cellProps: {
        align: 'center',
        sx: {
          minWidth: '100px',
          width: '100px',
          whiteSpace: 'nowrap',
          '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.02)',
          },
        },
      },
    },
    {
      key: 'calcium',
      header: 'Calcium (mg)',
      cellProps: { align: 'center' },
    },
    {
      key: 'phosphorus',
      header: 'Phosphorous (mg)',
      cellProps: { align: 'center' },
    },
    {
      key: 'magnesium',
      header: 'Magnesium (mg)',
      cellProps: { align: 'center' },
    },
    {
      key: 'sodium',
      header: 'Sodium (mg)',
      cellProps: { align: 'center' },
    },
    {
      key: 'potassium',
      header: 'Potassium (mg)',
      cellProps: { align: 'center' },
    },
    {
      key: 'iron',
      header: ' Iron (mg)',
      cellProps: { align: 'center' },
    },
  ];

  const headers = useMemo(
    () => (type === 'macro' ? macroHeaders : microHeaders),
    [type, sortField, sortDirection]
  );

  const sortedData = useMemo(() => {
    if (!sortField) return data;

    return [...data].sort((a, b) => {
      const aValue = a[sortField as keyof NutritionSummaryDataPoint];
      const bValue = b[sortField as keyof NutritionSummaryDataPoint];

      // Handle null/undefined values
      if (aValue === undefined || aValue === null)
        return sortDirection === 'asc' ? -1 : 1;
      if (bValue === undefined || bValue === null)
        return sortDirection === 'asc' ? 1 : -1;

      // Compare values
      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });
  }, [data, sortField, sortDirection]);

  const rows: RowV2[] = sortedData.map((item) => {
    if (type === 'macro') {
      const row: Record<string, CellV2 | string> = {
        date: {
          value: formatDate(item.date),
          cellProps: {
            align: 'center' as const,
            sx: {
              minWidth: '85px',
              width: '85px',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            },
          },
        },
        meals: {
          value: formatValue(item.meals, 0),
          cellProps: {
            align: 'center' as const,
            sx: {
              minWidth: '50px',
              width: '50px',
            },
          },
        },
        snacks: {
          value: formatValue(item.snacks, 0),
          cellProps: {
            align: 'center' as const,
            sx: {
              minWidth: '55px',
              width: '55px',
            },
          },
        },
        calories: {
          value: formatValue(item.calories),
          cellProps: { align: 'center' as const },
        },
        carbs: {
          value: formatMacroCell(item.carbs, item.carbs_percentage),
          cellProps: {
            align: 'center' as const,
            sx: {
              minWidth: '75px',
              width: '75px',
              py: 1.5,
            },
          },
        },
        protein: {
          value: formatMacroCell(item.protein, item.protein_percentage),
          cellProps: {
            align: 'center' as const,
            sx: {
              minWidth: '75px',
              width: '75px',
              py: 1.5,
            },
          },
        },
        fat: {
          value: formatMacroCell(item.fat, item.fat_percentage),
          cellProps: {
            align: 'center' as const,
            sx: {
              minWidth: '75px',
              width: '75px',
              py: 1.5,
            },
          },
        },
        sfa: {
          value: formatValue(item.sfa),
          cellProps: { align: 'center' as const },
        },
        mufa: {
          value: formatValue(item.mufa),
          cellProps: { align: 'center' as const },
        },
        pufa: {
          value: formatValue(item.pufa),
          cellProps: { align: 'center' as const },
        },
        cholesterol: {
          value: formatValue(item.cholesterol),
          cellProps: { align: 'center' as const },
        },
        fiber: {
          value: formatValue(item.fiber),
          cellProps: { align: 'center' as const },
        },
        sugar: {
          value: formatValue(item.sugar),
          cellProps: {
            align: 'center' as const,
            sx: {
              minWidth: '55px',
              width: '55px',
            },
          },
        },
        oil: {
          value: formatValue(item.oil),
          cellProps: {
            align: 'center' as const,
            sx: {
              minWidth: '45px',
              width: '45px',
            },
          },
        },
        salt: {
          value: formatValue(item.salt),
          cellProps: {
            align: 'center' as const,
            sx: {
              minWidth: '45px',
              width: '45px',
            },
          },
        },
      };
      return row as RowV2;
    } else {
      const row: Record<string, CellV2 | string> = {
        date: {
          value: formatDate(item.date),
          cellProps: {
            align: 'center' as const,
            sx: {
              minWidth: '85px',
              width: '85px',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            },
          },
        },
        calcium: {
          value: formatValue(item.calcium),
          cellProps: { align: 'center' as const },
        },
        phosphorus: {
          value: formatValue(item.phosphorus),
          cellProps: { align: 'center' as const },
        },
        magnesium: {
          value: formatValue(item.magnesium),
          cellProps: { align: 'center' as const },
        },
        sodium: {
          value: formatValue(item.sodium),
          cellProps: { align: 'center' as const },
        },
        potassium: {
          value: formatValue(item.potassium),
          cellProps: { align: 'center' as const },
        },
        iron: {
          value: formatValue(item.iron),
          cellProps: { align: 'center' as const },
        },
      };
      return row as RowV2;
    }
  });

  // Table styling configuration
  const tableContainerSx = {
    width: '100%',
    // Header styling - light blue background, 12px regular font
    '& .MuiTableHead-root .MuiTableCell-root': {
      backgroundColor: '#E6F6FF',
      fontWeight: 500, // Slightly bolder for headers
      fontSize: '12px', // Back to 12px
      color: '#334155',
      padding: '6px 3px', // Reduced from 8px 4px
      borderBottom: '1px solid #DAE1E7',
      borderRight: '1px solid #DAE1E7',
      whiteSpace: 'normal',
      wordWrap: 'break-word',
      textAlign: 'center',
      lineHeight: '1.1',
      minWidth: '60px',
    },
    // Row styling - 14px font size
    '& .MuiTableBody-root .MuiTableCell-root': {
      fontSize: '13px', // Back to 14px
      padding: '6px 3px', // Reduced from 8px 4px
      borderBottom: '1px solid #DAE1E7',
      borderRight: '1px solid #DAE1E7',
      whiteSpace: 'normal',
      wordWrap: 'break-word',
      textAlign: 'center',
      lineHeight: '1.1',
    },
    // Table container styling
    '& .MuiTable-root': {
      width: '100%',
      tableLayout: 'fixed',
      borderRadius: '8px',
    },
    // Table container
    '& .MuiTableContainer-root': {
      borderRadius: '8px',
      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
      width: '100%',
      overflowX: 'hidden',
      border: '1px solid #DAE1E7',
    },
    // Table cell styling
    '& .MuiTableCell-root': {
      maxWidth: '120px',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
    },
    // Ensure table takes full width
    '& .MuiTableContainer-root .MuiTable-root': {
      minWidth: '100%',
    },
    // Remove the last cell border
    '& .MuiTableCell-root:last-child': {
      borderRight: 'none',
    },
    // Alternate row colors for better readability
    '& .MuiTableBody-root .MuiTableRow-root:nth-of-type(even)': {
      backgroundColor: '#F8F9FA',
    },
    '& .MuiTableBody-root .MuiTableRow-root:hover': {
      backgroundColor: '#F1F5F9',
    },
  };

  return (
    <div className={`w-full ${className}`}>
      <TableV2
        headers={headers}
        rows={rows}
        loading={loading}
        tableContainerProps={{
          sx: tableContainerSx,
          style: {
            width: '100%',
            borderRadius: '8px',
          },
        }}
        noDataMessage="No nutrition data available"
      />
    </div>
  );
};

export default NutritionSummaryTable;
